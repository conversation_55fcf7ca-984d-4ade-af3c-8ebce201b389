2025-07-24 17:15:22,158 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:15:24,236 - LLMQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 222, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 17:16:25,411 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:16:27,481 - LLMQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 222, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 17:16:27,483 - LLMQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行問答測試。
2025-07-24 17:18:33,600 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:18:35,668 - LLMQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 222, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 17:18:35,671 - LLMQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行問答測試。
2025-07-24 17:19:55,637 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:19:57,709 - LLMQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 222, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 17:43:34,031 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:43:36,104 - LLMQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 222, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 17:43:36,108 - LLMQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行問答測試。
2025-07-24 17:45:29,695 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:45:31,759 - LLMQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 222, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 17:45:31,784 - LLMQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行問答測試。
2025-07-24 17:56:08,588 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:56:12,822 - LLMQAService - CRITICAL - Ollama 連通性測試失敗：無法連接到服務器。請確保 Ollama 服務已啟動並監聽在 http://localhost:11434。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
        (self._dns_host, self.port),
    ...<2 lines>...
        socket_options=self.socket_options,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
    ~~~~~~~~~~~~^^^^
ConnectionRefusedError: [WinError 10061] 無法連線，因為目標電腦拒絕連線。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
    ~~~~~~~~~~~~^
        method,
        ^^^^^^^
    ...<6 lines>...
        enforce_content_length=enforce_content_length,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
    ~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\http\client.py", line 1333, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\http\client.py", line 1093, in _send_output
    self.send(msg)
    ~~~~~~~~~^^^^^
  File "C:\Python313\Lib\http\client.py", line 1037, in send
    self.connect()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
                ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
        self, f"Failed to establish a new connection: {e}"
    ) from e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x000002180E34B380>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002180E34B380>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 221, in test_ollama_connection
    response = requests.post(OLLAMA_API_URL, headers=headers, json=data, timeout=30)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002180E34B380>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。'))
2025-07-24 17:56:12,938 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 17:56:16,340 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 17:56:42,640 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 17:56:46,748 - LLMQAService - CRITICAL - Ollama 連通性測試失敗：無法連接到服務器。請確保 Ollama 服務已啟動並監聽在 http://localhost:11434。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connection.py", line 198, in _new_conn
    sock = connection.create_connection(
        (self._dns_host, self.port),
    ...<2 lines>...
        socket_options=self.socket_options,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\util\connection.py", line 85, in create_connection
    raise err
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\util\connection.py", line 73, in create_connection
    sock.connect(sa)
    ~~~~~~~~~~~~^^^^
ConnectionRefusedError: [WinError 10061] 無法連線，因為目標電腦拒絕連線。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 493, in _make_request
    conn.request(
    ~~~~~~~~~~~~^
        method,
        ^^^^^^^
    ...<6 lines>...
        enforce_content_length=enforce_content_length,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connection.py", line 494, in request
    self.endheaders()
    ~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\http\client.py", line 1333, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python313\Lib\http\client.py", line 1093, in _send_output
    self.send(msg)
    ~~~~~~~~~^^^^^
  File "C:\Python313\Lib\http\client.py", line 1037, in send
    self.connect()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connection.py", line 325, in connect
    self.sock = self._new_conn()
                ~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connection.py", line 213, in _new_conn
    raise NewConnectionError(
        self, f"Failed to establish a new connection: {e}"
    ) from e
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x00000255D6896BA0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\urllib3\util\retry.py", line 519, in increment
    raise MaxRetryError(_pool, url, reason) from reason  # type: ignore[arg-type]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
urllib3.exceptions.MaxRetryError: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000255D6896BA0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_qa_service.py", line 221, in test_ollama_connection
    response = requests.post(OLLAMA_API_URL, headers=headers, json=data, timeout=30)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\adapters.py", line 700, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPConnectionPool(host='localhost', port=11434): Max retries exceeded with url: /api/generate (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000255D6896BA0>: Failed to establish a new connection: [WinError 10061] 無法連線，因為目標電腦拒絕連線。'))
2025-07-24 17:56:46,898 - LLMQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行問答測試。
2025-07-24 18:03:17,529 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:03:31,016 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:03:31,016 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:03:31,017 - LLMQAService - INFO - Ollama 服務準備就緒。
2025-07-24 18:03:31,017 - LLMQAService - INFO - 
--- 測試默認問答 ---
2025-07-24 18:03:48,313 - LLMQAService - INFO - LLM 回答: 這個產品（CPX-900 K1A）採用高清晰度多媒體介面(HDMI™)技術，能夠傳輸最佳品質的數位影像訊號。
2025-07-24 18:03:48,313 - LLMQAService - INFO - 
--- 測試字段提取 ---
2025-07-24 18:03:50,524 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:04:19,959 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:04:19,960 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:04:19,964 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:04:23,538 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:04:30,148 - LLMQAService - INFO - 提取結果: {'item_name': '高清晰度多媒體介面(HDMI™)', 'category': '硬體規格', 'description': '本產品採用高清晰度多媒體介面(HDMI™)技術,使用此介面能傳輸最佳品質的數位影像訊號。', 'level': 2, 'parent_name': 'CPX-900 K1A'}
2025-07-24 18:06:43,367 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:06:46,202 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:06:46,202 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:06:46,204 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:06:49,397 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:16:45,162 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:16:56,764 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:16:56,765 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:16:56,765 - LLMQAService - INFO - Ollama 服務準備就緒。
2025-07-24 18:16:56,765 - LLMQAService - INFO - 
--- 測試默認問答 ---
2025-07-24 18:17:12,896 - LLMQAService - INFO - LLM 回答: 這個產品（CPX-900 K1A）採用高清晰度多媒體介面(HDMI™)技術，可以傳輸最佳品質的數位影像訊號。
2025-07-24 18:17:12,897 - LLMQAService - INFO - 
--- 測試字段提取 ---
2025-07-24 18:17:53,572 - LLMQAService - INFO - 提取結果: {'item_name': '高清晰度多媒體介面(HDMI™)技術', 'category': '硬體規格', 'description': '本產品採用高清晰度多媒體介面(HDMI™)技術,使用此介面能傳輸最佳品質的數位影像訊號。', 'level': 2, 'parent_name': 'CPX-900 K1A'}
2025-07-24 18:18:05,297 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:18:08,819 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:18:08,820 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:18:08,821 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:18:11,692 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:18:11,779 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:18:14,352 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:18:56,622 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:18:58,843 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:18:59,733 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:18:59,958 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:04,068 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:04,491 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:19:04,754 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:07,478 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:19:07,479 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:19:07,479 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:09,816 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:09,902 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:12,391 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:43,628 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:43,784 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:48,352 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:48,725 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:49,251 - LLMQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 18:19:52,034 - LLMQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 18:19:52,034 - LLMQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 18:19:52,035 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:54,411 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:19:54,504 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:19:57,105 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:21:00,851 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:21:03,329 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:21:18,589 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:21:21,067 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:22:02,008 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:22:04,530 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:23:51,506 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:23:53,895 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:24:10,413 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:24:12,904 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:24:37,696 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:24:40,311 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:24:50,918 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:24:53,330 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:25:33,227 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:25:35,568 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:27:43,637 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:27:44,820 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:27:47,125 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:27:48,452 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:27:48,542 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:27:50,800 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:28:04,818 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:28:05,032 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:28:09,042 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:28:09,390 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:28:59,476 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:29:01,147 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 18:29:20,048 - UIInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 18:29:22,004 - UIInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
