# Ark_Knowledge_Engine/06_Scripts/stage1_preprocessing_module.py

import pandas as pd
import os
import uuid
from datetime import datetime
import re # 用於檔名解析

# 嘗試導入文件解析庫
try:
    import fitz # PyMuPDF for PDF
    print("PyMuPDF (fitz) 模組已載入。")
except ImportError:
    print("警告: PyMuPDF (fitz) 模組未安裝。PDF 檔案內容將無法提取。請執行 'pip install PyMuPDF'。")
    fitz = None

try:
    from docx import Document # python-docx for DOCX
    print("python-docx 模組已載入。")
except ImportError:
    print("警告: python-docx 模組未安裝。DOCX 檔案內容將無法提取。請執行 'pip install python-docx'。")
    Document = None

# --- 配置區 ---
# 假設所有原始 Excel Sheet (零件基礎庫, 產品總覽, 問題追蹤) 整合在一個檔案中
EXCEL_MASTER_FILE = os.path.join('..', 'Raw_Data_Sources', 'Excel_Sheets_Master.xlsx')
# 存放以零件料號為檔名的 BOM 表的資料夾
PART_BOM_FILES_DIR = os.path.join('..', 'Raw_Data_Sources', 'Part_BOM_Files')
# 存放產品說明書的資料夾
MANUALS_DIR = os.path.join('..', 'Raw_Data_Sources', 'Product_Manuals')
# 臨時輸出數據的資料夾
TEMP_OUTPUT_DIR = os.path.join('..', 'Temp_Processed_Data')

# 確保輸出資料夾存在
os.makedirs(TEMP_OUTPUT_DIR, exist_ok=True)

# 內部欄位映射字典 (用於將原始 BOM 檔案內的欄位名映射到標準名稱)
# 請根據您的實際檔名和內容調整
BOM_INTERNAL_COL_MAP = {
    '零件編號': 'PartNumberID_BOMInternal', # 標識為BOM內部的零件ID
    '品名規格': 'PartName_BOMInternal',
    '規格備註': 'Specification_BOMInternal',
    '單位': 'Unit_BOMInternal',
    '使用數量': 'Quantity_BOMInternal',
    '位置': 'Location_BOMInternal',
    '零件類別': 'PartCategory_BOMInternal'
}

# --- 輔助函數 ---

def parse_product_info_from_bom_filename(filename):
    """
    從 BOM 檔案名稱中解析出 ProductName 和 ProductID。
    這個函數需要根據您實際的檔名模式進行精確調整。
    範例檔名：FKTCPX900K2R 900K2R BOM 20230203.xlsx
    假設：檔名中第二個部分是 ProductID (例如 900K2R)，第一個部分是 ProductName (例如 FKTCPX900K2R)
    """
    try:
        # 移除副檔名
        base_name = os.path.splitext(filename)[0]
        # 以空格分割
        name_parts = base_name.split(' ')

        if len(name_parts) >= 2:
            # 假設第一個部分是 ProductName (包含前綴)
            product_name = name_parts[0]
            # 假設第二個部分是 ProductID (純粹的ID)
            product_id = name_parts[1]
            return product_name, product_id
        elif len(name_parts) == 1:
            # 如果只有一個部分，假設它既是 ProductName 也是 ProductID
            return name_parts[0], name_parts[0]
        else:
            return None, None
    except Exception as e:
        print(f"警告: 解析檔名 '{filename}' 失敗: {e}")
        return None, None

def extract_text_from_pdf(file_path):
    """從 PDF 檔案中提取所有頁面的文本內容。"""
    if not fitz: return "PyMuPDF 模組未載入，無法提取 PDF 內容。"
    text_content = []
    try:
        doc = fitz.open(file_path)
        for page_num in range(doc.page_count):
            page = doc.load_page(page_num)
            text_content.append(page.get_text())
        doc.close()
    except Exception as e:
        return f"錯誤: 無法提取 PDF 文本: {e}"
    return "\n".join(text_content)

def extract_text_from_docx(file_path):
    """從 DOCX 檔案中提取所有段落的文本內容。"""
    if not Document: return "python-docx 模組未載入，無法提取 DOCX 內容。"
    text_content = []
    try:
        doc = Document(file_path)
        for para in doc.paragraphs:
            text_content.append(para.text)
    except Exception as e:
        return f"錯誤: 無法提取 DOCX 文本: {e}"
    return "\n".join(text_content)

def extract_text_from_txt(file_path):
    """從 TXT 檔案中提取文本內容。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"錯誤: 無法提取 TXT 文本: {e}"

def extract_text_from_xlsx_as_raw(file_path):
    """從 XLSX 檔案中提取所有 Sheet 的所有單元格內容作為原始文本。"""
    try:
        xls = pd.ExcelFile(file_path)
        all_sheet_content = []
        for sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name)
            # 將所有單元格內容轉換為字符串並連接
            all_sheet_content.append(df.to_string(index=False, header=False))
        return "\n".join(all_sheet_content)
    except Exception as e:
        return f"錯誤: 無法提取 XLSX 原始文本: {e}"

# --- 主要處理函數 ---

def load_excel_sheets_master(excel_path):
    """載入 Excel_Sheets_Master.xlsx 中的必要 Sheet。"""
    print(f"正在載入核心 Excel 檔案: {excel_path}...")
    xls = pd.ExcelFile(excel_path)
    data = {}
    
    required_sheets = {
        '零件基礎庫': ['PartNumberID', 'PartName', 'PartCategory', 'Specification', 'Notes'],
        '產品總覽': ['PartNumberID', 'ProductName', 'ProductType', 'ProductStatus', 'BOMLink', 'ManualLink', 'Notes'],
        '問題追蹤': ['問題 ID', '關聯功能 ID', '機型名稱', '問題描述', '問題狀態', '處理人', '測試日期', '回報人', '改善方案/回覆', '改善結果驗證', '驗證日期', '登入連結']
    }

    for sheet_name, expected_cols_prefix in required_sheets.items(): # Changed to _prefix as a reminder
        if sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name)
            # Note: For strict column validation, you might want to check exact names,
            # but for robustness, we'll assume they are there in some form.
            data[sheet_name] = df
            print(f"  - Sheet '{sheet_name}' 載入成功，共 {len(df)} 筆資料。")
        else:
            print(f"錯誤: 缺少必要的 Sheet '{sheet_name}'。請檢查 '{excel_path}'。")
            data[sheet_name] = pd.DataFrame(columns=expected_cols_prefix) # 返回空DataFrame避免報錯
    
    return data

def process_part_bom_files(part_bom_dir, part_lib_df, product_overview_df):
    """
    遍歷 Part_BOM_Files 資料夾，讀取每個 BOM 檔案，
    解析檔名，映射欄位，生成 BOMID，並與零件基礎庫、產品總覽關聯。
    """
    print(f"正在處理 Part_BOM_Files 資料夾: {part_bom_dir}...")
    all_processed_boms = []

    # 確保關聯用的 ID 都是字符串類型
    # 這裡假設零件基礎庫的 PartNumberID 和 產品總覽的 ProductName 是正確的關聯鍵
    part_lib_df['PartNumberID'] = part_lib_df['PartNumberID'].astype(str)
    product_overview_df['ProductName'] = product_overview_df['ProductName'].astype(str)
    
    for filename in os.listdir(part_bom_dir):
        file_path = os.path.join(part_bom_dir, filename)
        if os.path.isfile(file_path) and filename.lower().endswith('.xlsx'):
            product_name_from_file, product_id_from_file = parse_product_info_from_bom_filename(filename)
            
            if not product_name_from_file or not product_id_from_file:
                print(f"警告: 無法從檔名 '{filename}' 解析產品信息 (ProductName, ProductID)，跳過此 BOM 檔案。")
                continue
            
            # 檢查這個 ProductName 是否存在於產品總覽中
            # 注意: 產品總覽的 PartNumberID 是產品本身的唯一ID，ProductName 是顯示名稱
            # 這裡我們用 ProductName_FromFilename 去匹配產品總覽的 ProductName
            if product_name_from_file not in product_overview_df['ProductName'].values:
                print(f"警告: 檔名 '{filename}' 中的產品名稱 '{product_name_from_file}' 未在產品總覽中找到。這條 BOM 記錄的產品總覽信息將會是空的。")
            
            try:
                df_bom_single_file = pd.read_excel(file_path, sheet_name=0) # 讀取第一個 Sheet
                
                # 執行內部欄位映射
                mapped_bom_df = df_bom_single_file.rename(columns={
                    old_col: new_col for old_col, new_col in BOM_INTERNAL_COL_MAP.items() if old_col in df_bom_single_file.columns
                })
                
                # 檢查映射後的關鍵欄位是否存在
                required_internal_cols = ['PartNumberID_BOMInternal', 'Quantity_BOMInternal']
                missing_internal_cols = [col for col in required_internal_cols if col not in mapped_bom_df.columns]
                if missing_internal_cols:
                    print(f"警告: BOM 檔案 '{filename}' 映射後缺少關鍵欄位: {missing_internal_cols}，跳過此 BOM 檔案。")
                    continue

                # 添加從檔名解析出的產品信息到每一行
                mapped_bom_df['ProductID_FromFilename'] = product_id_from_file
                mapped_bom_df['ProductName_FromFilename'] = product_name_from_file
                
                # 為每行生成唯一的 BOMID (每個產品-零件組合的實例)
                mapped_bom_df['BOMID'] = [f"BOM-{uuid.uuid4().hex}" for _ in range(len(mapped_bom_df))]
                mapped_bom_df['EffectiveDate_BOM'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                all_processed_boms.append(mapped_bom_df)
                print(f"  - 處理 BOM 檔案 '{filename}' 成功。")

            except Exception as e:
                print(f"錯誤: 無法讀取或處理 BOM 檔案 '{filename}': {e}")
    
    if not all_processed_boms:
        print("沒有 BOM 檔案被成功處理。返回空的 DataFrame。")
        return pd.DataFrame()

    # 合併所有處理後的單一 BOM 檔案數據
    combined_raw_boms = pd.concat(all_processed_boms, ignore_index=True)
    
    # 進行最終的整合關聯
    # 關聯到產品總覽 (ProductName_FromFilename 對應產品總覽的 ProductName)
    combined_boms_with_product = pd.merge(
        combined_raw_boms,
        product_overview_df[['ProductName', 'PartNumberID', 'ProductType', 'ProductStatus', 'BOMLink', 'ManualLink', 'Notes']].rename(columns={'Notes': 'ProductOverview_Notes', 'PartNumberID': 'Product_PartNumberID'}),
        left_on='ProductName_FromFilename',
        right_on='ProductName',
        how='left',
        suffixes=('_BOMFile', '_ProductOverview')
    )
    # 避免重複 ProductName 列
    combined_boms_with_product.drop(columns=['ProductName_ProductOverview'], inplace=True, errors='ignore') 

    # 關聯到零件基礎庫 (PartNumberID_BOMInternal 對應 零件基礎庫的 PartNumberID)
    final_integrated_boms = pd.merge(
        combined_boms_with_product,
        part_lib_df[['PartNumberID', 'PartName', 'PartCategory', 'Specification', 'Notes']].rename(columns={'Notes': 'PartLib_Notes'}),
        left_on='PartNumberID_BOMInternal',
        right_on='PartNumberID',
        how='left',
        suffixes=('_Merged', '_PartLib') # 再次處理後綴
    )
    # 清理因合併產生的重複列或不明確的列
    final_integrated_boms.drop(columns=[col for col in final_integrated_boms.columns if col.endswith('_PartLib') and col != 'PartLib_Notes'], inplace=True)
    final_integrated_boms.rename(columns={'PartName_Merged': 'PartName_PartLib', # 確保名稱一致
                                         'PartCategory_Merged': 'PartCategory_PartLib',
                                         'Specification_Merged': 'Specification_PartLib'}, inplace=True)

    print(f"所有 BOM 檔案處理並整合完成，共 {len(final_integrated_boms)} 筆整合 BOM 數據。")
    return final_integrated_boms

def extract_content_from_manuals(manuals_dir, df_product_overview):
    """
    遍歷說明書資料夾，讀取不同格式的檔案，提取文本內容。
    """
    print(f"正在提取說明書內容: {manuals_dir}...")
    manual_extracted_data = []

    for filename in os.listdir(manuals_dir):
        file_path = os.path.join(manuals_dir, filename)
        if os.path.isfile(file_path):
            product_name = os.path.splitext(filename)[0] # 檔案名稱即為 ProductName
            file_extension = os.path.splitext(filename)[1].lower()

            # 檢查 ProductName 是否在產品總覽中
            if product_name not in df_product_overview['ProductName'].values:
                print(f"警告: 說明書 '{filename}' 的產品名稱 '{product_name}' 未在產品總覽中找到。將繼續處理，但可能缺少產品相關上下文。")
            
            extracted_text = "無法提取內容或檔案格式不支援"
            if file_extension == '.pdf':
                extracted_text = extract_text_from_pdf(file_path)
            elif file_extension == '.docx':
                extracted_text = extract_text_from_docx(file_path)
            elif file_extension == '.txt':
                extracted_text = extract_text_from_txt(file_path)
            elif file_extension == '.xlsx':
                extracted_text = extract_text_from_xlsx_as_raw(file_path) # 將xlsx作為原始文本提取
            else:
                print(f"警告: 不支援的說明書檔案格式: {filename}")

            manual_extracted_data.append({
                'SourceType': '說明書',
                'SourceFile': filename,
                'AssociatedProductName': product_name,
                'OriginalID': product_name, # 說明書的原始ID就是產品名
                'ExtractedRawText': extracted_text, # 提取到的原始文本，待後續NLP處理
                # 以下欄位將在人工審核或AI Agent階段填充
                'PotentialItemName': '', 
                'PotentialCategory': '', 
                'ExtractedDescription': '', # 更精煉的描述
                'SuggestedLevel': '', 
                'SuggestedParentID': '', 
                'ManualReviewFlag': True, # 默認需要人工審核
                'ManualReviewNotes': '', # 待人工填寫
                'BOMLink': df_product_overview[df_product_overview['ProductName'] == product_name]['BOMLink'].iloc[0] if product_name in df_product_overview['ProductName'].values and not df_product_overview[df_product_overview['ProductName'] == product_name]['BOMLink'].empty else '',
                'ManualLink': df_product_overview[df_product_overview['ProductName'] == product_name]['ManualLink'].iloc[0] if product_name in df_product_overview['ProductName'].values and not df_product_overview[df_product_overview['ProductName'] == product_name]['ManualLink'].empty else '',
            })
    
    print(f"說明書內容提取完成，共處理 {len(manual_extracted_data)} 份檔案。")
    return pd.DataFrame(manual_extracted_data)

def run_preprocessing_module():
    """
    執行工作表預處理模組的主函數。
    """
    print("--- 啟動「方舟知識引擎」第一階段：「工作表預處理模組」---")

    # 1. 載入核心 Excel Sheet (零件基礎庫, 產品總覽, 問題追蹤)
    excel_sheets_data = load_excel_sheets_master(EXCEL_MASTER_FILE)
    
    # 檢查是否所有核心 Sheet 都已載入
    if not all(k in excel_sheets_data for k in ['零件基礎庫', '產品總覽', '問題追蹤']):
        print("錯誤: 未能載入所有必要的 Excel Master Sheet。請檢查檔案和Sheet名稱。")
        return

    # 2. 處理分散的產品 BOM 檔案
    processed_bom_df = process_part_bom_files(
        PART_BOM_FILES_DIR,
        excel_sheets_data['零件基礎庫'],
        excel_sheets_data['產品總覽']
    )

    # 3. 處理說明書檔案 (提取原始文本內容)
    manual_extracted_df = extract_content_from_manuals(
        MANUALS_DIR,
        excel_sheets_data['產品總覽']
    )

    # 4. 整合所有數據到「臨時整合數據集」
    print("正在整合所有數據以生成「臨時整合數據集」...")

    # 定義最終臨時數據集的欄位結構
    final_temp_columns = [
        'SourceType', 'SourceFile', 'AssociatedProductName', 'OriginalID', 
        'ExtractedRawText', 
        'PotentialItemName', 'PotentialCategory', 'ExtractedDescription',
        'SuggestedLevel', 'SuggestedParentID', 
        'BOMID', 
        'PartNumberID_BOM', # 來自BOM內部的零件ID
        'PartName_PartLib', # 來自零件基礎庫的PartName
        'Specification_PartLib', # 來自零件基礎庫的Specification
        'Location_BOM', # 來自BOM內部的Location
        'PartCategory_PartLib', # 來自零件基礎庫的PartCategory
        'Quantity_BOM', # 來自BOM內部的Quantity
        'Unit_BOM', # 來自BOM內部的Unit
        'BOMType_BOM', # 來自BOM內部的BOMType (如果存在)
        'Version_BOM', # 來自BOM內部的Version (如果存在)
        'EffectiveDate_BOM', # 來自BOM的生效日期
        'OriginalNotes', 
        'BOMLink', 'ManualLink', 
        '問題追蹤ID', '問題狀態', '問題描述_問題追蹤', # 增加問題描述，避免信息丟失
        '人工審核狀態', '人工修正建議', 
        'LastProcessedDate' 
    ]
    
    # 初始化一個空的 DataFrame 來存放整合數據
    df_temp_combined = pd.DataFrame(columns=final_temp_columns)

    # 合併 Excel 相關的數據（BOM 數據、問題追蹤、產品總覽等）
    if not processed_bom_df.empty:
        df_excel_processed = processed_bom_df.copy()
        df_excel_processed['SourceType'] = 'Excel_BOM_Integrated'
        df_excel_processed['SourceFile'] = EXCEL_MASTER_FILE + " / " + "Part_BOM_Files" # 標示來源
        df_excel_processed['OriginalID'] = df_excel_processed['PartNumberID_BOMInternal'] # 原始零件ID
        df_excel_processed['AssociatedProductName'] = df_excel_processed['ProductName_FromFilename'] # 產品名
        
        # 核心BOM信息
        df_excel_processed['BOMID'] = df_excel_processed['BOMID'] 
        df_excel_processed['PartNumberID_BOM'] = df_excel_processed['PartNumberID_BOMInternal']
        df_excel_processed['Quantity_BOM'] = df_excel_processed['Quantity_BOMInternal']
        df_excel_processed['Unit_BOM'] = df_excel_processed['Unit_BOMInternal']
        df_excel_processed['Location_BOM'] = df_excel_processed['Location_BOMInternal']
        df_excel_processed['EffectiveDate_BOM'] = df_excel_processed['EffectiveDate_BOM']
        
        # 從零件基礎庫帶入的信息
        df_excel_processed['PartName_PartLib'] = df_excel_processed['PartName'] 
        df_excel_processed['Specification_PartLib'] = df_excel_processed['Specification'] 
        df_excel_processed['PartCategory_PartLib'] = df_excel_processed['PartCategory'] 
        
        # 其他可用的 BOM 信息
        df_excel_processed['BOMType_BOM'] = df_excel_processed.get('BOMType_BOMInternal', '') # 可能不存在
        df_excel_processed['Version_BOM'] = df_excel_processed.get('Version_BOMInternal', '') # 可能不存在

        # 合併各種 Notes
        df_excel_processed['OriginalNotes'] = df_excel_processed['PartLib_Notes'].fillna('') + "; " + \
                                            df_excel_processed['ProductOverview_Notes'].fillna('') + "; " + \
                                            df_excel_processed.get('Notes_BOMInternal', pd.Series()).fillna('') # 確保Notes_BOMInternal存在
        df_excel_processed['OriginalNotes'] = df_excel_processed['OriginalNotes'].replace(r';\s*$', '', regex=True).replace(r'^\s*;', '', regex=True).str.strip()

        # 將 BOMLink 和 ManualLink 從產品總覽直接帶入
        df_excel_processed['BOMLink'] = df_excel_processed['BOMLink']
        df_excel_processed['ManualLink'] = df_excel_processed['ManualLink']

        # 暫時為空或預設給後續階段使用
        df_excel_processed['ExtractedRawText'] = '' # 主要給說明書
        df_excel_processed['PotentialItemName'] = df_excel_processed['PartName_PartLib'] # 零件名稱作為潛在功能名
        df_excel_processed['PotentialCategory'] = df_excel_processed['PartCategory_PartLib'] # 零件分類作為潛在分類
        df_excel_processed['ExtractedDescription'] = df_excel_processed['Specification_PartLib'] # 規格描述作為初步描述
        df_excel_processed['SuggestedLevel'] = ''
        df_excel_processed['SuggestedParentID'] = ''
        df_excel_processed['問題追蹤ID'] = ''
        df_excel_processed['問題狀態'] = ''
        df_excel_processed['問題描述_問題追蹤'] = ''
        df_excel_processed['人工審核狀態'] = '待審核'
        df_excel_processed['人工修正建議'] = ''
        df_excel_processed['LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        df_temp_combined = pd.concat([df_temp_combined, df_excel_processed[final_temp_columns]], ignore_index=True)

    # 合併說明書提取的數據
    if not manual_extracted_df.empty:
        df_manual_processed = manual_extracted_df.copy()
        df_manual_processed['SourceType'] = '說明書'
        df_manual_processed['OriginalID'] = df_manual_processed['AssociatedProductName'] # 說明書的原始ID用產品名
        df_manual_processed['ExtractedRawText'] = df_manual_processed['ExtractedRawText']
        df_manual_processed['ExtractedDescription'] = df_manual_processed['ExtractedRawText'].apply(lambda x: x[:500] + '...' if isinstance(x, str) and len(x) > 500 else x) # 初步描述截斷，避免過長
        
        # 從產品總覽帶入連結信息 (已經在 extract_content_from_manuals 中完成)
        df_manual_processed['BOMLink'] = df_manual_processed['BOMLink']
        df_manual_processed['ManualLink'] = df_manual_processed['ManualLink']

        # 填充其他欄位 (與說明書數據不直接相關或需要後續填充)
        df_manual_processed['BOMID'] = '' 
        df_manual_processed['PartNumberID_BOM'] = ''
        df_manual_processed['PartName_PartLib'] = ''
        df_manual_processed['Specification_PartLib'] = ''
        df_manual_processed['Location_BOM'] = ''
        df_manual_processed['PartCategory_PartLib'] = ''
        df_manual_processed['PotentialItemName'] = '' # 待AI或人工從原始文本提取
        df_manual_processed['PotentialCategory'] = '' # 待AI或人工從原始文本提取
        df_manual_processed['SuggestedLevel'] = ''
        df_manual_processed['SuggestedParentID'] = ''
        df_manual_processed['OriginalNotes'] = '' 
        df_manual_processed['問題追蹤ID'] = ''
        df_manual_processed['問題狀態'] = ''
        df_manual_processed['問題描述_問題追蹤'] = ''
        df_manual_processed['人工審核狀態'] = '待審核'
        df_manual_processed['人工修正建議'] = ''
        df_manual_processed['LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        df_temp_combined = pd.concat([df_temp_combined, df_manual_processed[final_temp_columns]], ignore_index=True)

    # 合併問題追蹤數據
    if not excel_sheets_data['問題追蹤'].empty:
        df_issues = excel_sheets_data['問題追蹤'].copy()
        df_issues['SourceType'] = '問題追蹤'
        df_issues['SourceFile'] = EXCEL_MASTER_FILE
        df_issues['OriginalID'] = df_issues['問題 ID']
        df_issues['AssociatedProductName'] = df_issues['機型名稱']
        df_issues['ExtractedRawText'] = df_issues['問題描述'] # 問題描述作為原始文本
        df_issues['ExtractedDescription'] = df_issues['問題描述'] # 問題描述作為初步描述
        df_issues['問題追蹤ID'] = df_issues['問題 ID']
        df_issues['問題狀態'] = df_issues['問題狀態']
        df_issues['問題描述_問題追蹤'] = df_issues['問題描述']
        
        # 填充其他欄位
        df_issues['BOMID'] = ''
        df_issues['PartNumberID_BOM'] = ''
        df_issues['PartName_PartLib'] = ''
        df_issues['Specification_PartLib'] = ''
        df_issues['Location_BOM'] = ''
        df_issues['PartCategory_PartLib'] = ''
        df_issues['PotentialItemName'] = ''
        df_issues['PotentialCategory'] = ''
        df_issues['SuggestedLevel'] = ''
        df_issues['SuggestedParentID'] = ''
        df_issues['OriginalNotes'] = df_issues['改善方案/回覆'].fillna('') + "; " + df_issues['改善結果驗證'].fillna('')
        df_issues['OriginalNotes'] = df_issues['OriginalNotes'].replace(r';\s*$', '', regex=True).replace(r'^\s*;', '', regex=True).str.strip()
        df_issues['BOMLink'] = ''
        df_issues['ManualLink'] = ''
        df_issues['人工審核狀態'] = '待審核'
        df_issues['人工修正建議'] = ''
        df_issues['LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 確保問題追蹤的 dataframe 包含 final_temp_columns 中的所有欄位，
        # 缺失的欄位將被添加並設為 NaN，然後與主 dataframe 合併
        df_issues_for_concat = df_issues.reindex(columns=final_temp_columns)
        df_temp_combined = pd.concat([df_temp_combined, df_issues_for_concat], ignore_index=True)


    # 5. 輸出臨時整合表到 Excel
    output_path = os.path.join(TEMP_OUTPUT_DIR, 'temp_combined_for_review.xlsx')
    df_temp_combined.to_excel(output_path, index=False)
    print(f"「臨時整合數據集」已成功匯出至: {output_path}")
    print("--- 「工作表預處理模組」執行完畢 ---")

# --- 執行模組 ---
if __name__ == "__main__":
    run_preprocessing_module()