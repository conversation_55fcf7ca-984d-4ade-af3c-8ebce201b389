# Ark_Knowledge_Engine/Scripts/llm_classify_manual_data.py

import pandas as pd
import os
import requests  # 用於調用 Ollama API
import json
import logging
from datetime import datetime
import time
from tqdm import tqdm  # 用於顯示進度條
import re  # 用於解析 LLM 返回的 JSON

# --- 配置區 ---
TEMP_MANUAL_DATA_PATH = os.path.join('..', 'Temp_Processed_Data', 'temp_manual_extracted_data.xlsx')
LLM_PROCESSED_DATA_PATH = os.path.join('..', 'Temp_Processed_Data', 'temp_llm_processed_manual_data.xlsx')  # LLM 處理後的輸出路徑

OLLAMA_API_URL = "http://localhost:11434/api/generate"  # Ollama API 預設地址
OLLAMA_MODEL_NAME = "llama-13b-large"  # 使用您確認的模型名稱

# 定義標準化的功能分類選項，用於 LLM 的 Prompt 指導
STANDARD_CATEGORIES = [
    "硬體規格", "軟體功能", "操作流程", "系統設定", "故障排除", "配件說明", "服務信息", "未分類",
    "產品特色說明", "產品功能簡介", "接口說明", "規格說明", "性能參數", "連接方式", "安全須知", 
    "維護與保養", "圖像/多媒體", "表格數據", "原始表格", "版權聲明", "安全提示" 
]

# 批次處理大小，每次發送多少條記錄給 LLM
BATCH_SIZE = 1 # 維持 1，確保每個片段的上下文清晰，並利於調試

# --- 日誌配置 ---
LOG_DIR = os.path.join('..', 'Logs')
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, 'llm_classify.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('LLMClassifier')

# --- 輔助函數 ---

def load_manual_data(file_path):
    """載入說明書初步提取數據。"""
    if os.path.exists(file_path):
        logger.info(f"正在載入說明書數據: {file_path}")
        df = pd.read_excel(file_path)
        
        # 確保所有必要欄位都存在並是字符串，以避免 KeyError 或類型錯誤
        expected_cols = [
            'SourceType', 'SourceFile', 'AssociatedProductName', 'OriginalID', 
            'ExtractedRawText', 'ExtractedImagePaths', 'PdfPageImagePaths', 
            'ExtractedContent', 
            'ContentType', 
            'PageNumber', 
            'OriginalFontSize', 'IsBold', 'OriginalBBox', 
            'ExtractedDescription', 
            'PotentialItemName', 'PotentialCategory', 'SuggestedLevel', 'SuggestedParentName', 
            'ManualReviewFlag', 'ManualReviewNotes', 'BOMLink', 'ManualLink', 'LastProcessedDate',
            'BOMID', 'PartNumberID_BOM', 'PartName_PartLib', 'Specification_PartLib', 
            'Location_BOM', 'PartCategory_PartLib', 'Quantity_BOM', 'Unit_BOM',
            'BOMType_BOM', 'Version_BOM', 'EffectiveDate_BOM', 'OriginalNotes',
            '問題追蹤ID', '問題狀態', '問題描述_問題追蹤', 
            '人工審核狀態', '人工修正建議'
        ]
        for col in expected_cols:
            if col not in df.columns:
                df[col] = ''
            df[col] = df[col].fillna('').astype(str) 

        for col in ['PageNumber', 'OriginalFontSize']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
        
        if 'IsBold' in df.columns:
            df['IsBold'] = df['IsBold'].apply(lambda x: str(x).lower() == 'true') 

        logger.info(f"數據載入成功，共 {len(df)} 筆記錄。")
        return df
    else:
        logger.error(f"錯誤: 說明書初步提取數據檔案不存在: {file_path}。請先運行 extract_manual_data.py。")
        return pd.DataFrame()

def call_ollama_api(prompt, model_name, api_url):
    """
    調用本地 Ollama API 進行文本生成。
    """
    headers = {"Content-Type": "application/json"}
    data = {
        "model": model_name,
        "prompt": prompt,
        "stream": False, 
        "options": {
            "temperature": 0.1, 
            "num_predict": 2048 
        }
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=600) 
        response.raise_for_status() 
        result = response.json()
        logger.debug(f"Ollama 原始響應: {result}") # 新增日誌：打印原始響應
        return result['response']
    except requests.exceptions.ConnectionError as e:
        logger.critical(f"Ollama API 連線失敗: {e}. 請確保 Ollama 服務已啟動並監聽在 {api_url.replace('/api/generate', '')}。", exc_info=True)
        return None
    except requests.exceptions.Timeout as e:
        logger.error(f"Ollama API 請求超時: {e}", exc_info=True)
        return None
    except requests.exceptions.HTTPError as e:
        logger.error(f"Ollama API HTTP 錯誤: {e}. 狀態碼: {response.status_code}. 響應內容: {response.text[:200]}...", exc_info=True)
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Ollama API 返回無效 JSON: {e}. 原始響應: {response.text[:200]}...", exc_info=True)
        return None
    except KeyError:
        logger.error(f"Ollama API 響應格式錯誤，缺少 'response' 鍵。原始響應: {result}", exc_info=True) # 修正為 result
        return None
    except Exception as e:
        logger.error(f"調用 Ollama API 時發生未知錯誤: {e}", exc_info=True)
        return None

def parse_llm_response(response_text):
    """
    解析 LLM 返回的 JSON 字符串。
    優化解析邏輯，更健壯地提取 JSON。
    """
    logger.debug(f"嘗試解析 LLM 響應文本: {response_text[:500]}...") # 新增日誌：打印待解析文本
    try:
        # 優先尋找 ```json``` 包裹的內容
        json_match = re.search(r'```json\s*(\{.*\})\s*```', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            logger.debug(f"從 ```json``` 塊中提取到 JSON 字符串: {json_str[:200]}...")
        else:
            # 如果沒有 ```json``` 包裹，嘗試直接解析
            # 尋找從第一個 '{' 到最後一個 '}' 的內容
            json_start = response_text.find('{')
            json_end = response_text.rfind('}')
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = response_text[json_start : json_end + 1]
                logger.debug(f"直接提取到 JSON 字符串: {json_str[:200]}...")
            else:
                logger.warning(f"未在 LLM 響應中找到有效的 JSON 結構。原始文本: {response_text[:200]}...")
                return None
        
        parsed_data = json.loads(json_str)
        # 確保所有預期鍵都存在，並提供默認值
        return {
            'item_name': parsed_data.get('item_name', ''),
            'category': parsed_data.get('category', ''),
            'description': parsed_data.get('description', ''),
            'level': parsed_data.get('level', ''),
            'parent_name': parsed_data.get('parent_name', '')
        }
    except json.JSONDecodeError as e:
        logger.error(f"解析 LLM 返回的 JSON 失敗: {e}. 原始文本: {response_text[:500]}...", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"解析 LLM 響應時發生未知錯誤: {e}. 原始文本: {response_text[:500]}...", exc_info=True)
        return None

def generate_prompt(content_type, extracted_content, associated_product_name, standard_categories, page_number, original_font_size, is_bold, original_bbox):
    """
    構建發送給 LLM 的 Prompt，根據內容類型調整指令，並加入更多上下文元數據。
    """
    # 確保所有輸入參數都不是 None 或空字符串
    # 這裡將 page_number, original_font_size, is_bold, original_bbox 轉為更友好的字符串表示
    page_info = f"位於第 {page_number} 頁。" if page_number else ""
    font_info = f"字體大小約 {original_font_size}。" if original_font_size else ""
    bold_info = "且為加粗文本。" if is_bold else ""
    bbox_info = f"其在頁面上的邊界框為 {original_bbox}。" if original_bbox else ""

    context_hints = f"這是一個內容類型為 '{content_type}' 的知識片段。"
    if content_type.startswith('header'):
        context_hints += f"它是一個標題，{font_info}{bold_info}"
    elif content_type == 'table':
        context_hints += "這是一個表格，內容已轉換為 Markdown 格式。"
    elif content_type == 'image':
        context_hints += "這是一個圖片文件的描述，你可能需要根據其文件名來判斷可能的內容，並從上下文推斷其圖說或相關功能。"
    elif content_type == 'list_item':
        context_hints += "這是一個列表中的項目。"
    
    context_hints += f" {page_info}" # 將頁碼信息也加入提示

    categories_str = ", ".join(standard_categories)
    
    prompt_template = f"""
    你是一個經驗豐富的產品功能分析師，請你仔細閱讀以下產品說明書的知識片段。
    你的任務是從這段片段中識別並提取出最關鍵的「功能項目」(item_name)、其所屬的「分類」(category)、以及對應的「精煉描述」(description)、「建議層級」(level) 和「建議父級名稱」(parent_name)。

    請注意以下要求：
    1.  **專注於單一核心功能或規格點**：如果片段包含多個功能，請嘗試識別最主要的一個。
    2.  **項目名稱 (item_name)**：應簡潔、具體，能代表片段的核心功能或規格點。請使用繁體中文。例如：「處理器類型」、「音效模式」、「開機步驟」、「網路設定」、「HDMI 接口」、「USB 插槽」。
    3.  **分類 (category)**：從以下標準分類中選擇最合適的一個，如果都不適用，請使用「未分類」：{categories_str}。
    4.  **精煉描述 (description)**：將此知識片段的內容精煉為簡潔、準確的描述，字數約 30-100 字。只提取與該「項目名稱」直接相關的內容。
    5.  **建議層級 (level)**：判斷此功能項目在整個產品功能結構中的大概層級。通常：
        * 1：頂層總覽（如「產品特色說明」、「介面總覽」）
        * 2：主要功能模塊或大項（如「音效系統」、「網路設定」、「處理器」）
        * 3：具體功能點或規格細節（如「杜比全景聲」、「Wi-Fi 連線」、「核心數量」）
        * 4或更高：更細的參數或操作步驟。如果無法判斷，請填寫 0。
        **請根據 "{content_type}" 類型以及該片段的視覺特徵（如字體大小、是否加粗、邊界框等，若有提供）來推斷其層級。例如，header_h1 可能是 Level 1，header_h2 可能是 Level 2，普通段落或列表項可能是 Level 3 或 4。**
    6.  **建議父級名稱 (parent_name)**：如果此項目有明確的上級功能或模塊，請提供其「項目名稱」。如果為頂層項目，請填寫 "None" 或空字符串。
        **請根據文件結構和上下文判斷其所屬的最近一個上級「項目名稱」。**
    7.  **輸出格式**：請**只**以一個 JSON 對象返回結果，不要包含任何額外的文字解釋。請使用繁體中文的鍵名。

    ---
    產品名稱: {associated_product_name}
    內容上下文提示: {context_hints}
    知識片段內容:
    {extracted_content}
    ---

    你的 JSON 輸出範例：
    ```json
    {{
        "item_name": "產品特色說明",
        "category": "產品特色說明",
        "description": "這段說明解釋了產品的整體特色。",
        "level": 1,
        "parent_name": "None"
    }}
    ```
    ```json
    {{
        "item_name": "HDMI 輸出",
        "category": "接口說明",
        "description": "本產品採用高清晰度多媒體介面(HDMI)技術，可用於傳輸最佳品質的數位影音訊號。",
        "level": 2,
        "parent_name": "接口"
    }}
    ```
    ```json
    {{
        "item_name": "電源輸入孔",
        "category": "接口說明",
        "description": "DC電源插孔，本機器需採用DC 12V/5A電源供應器規格。",
        "level": 3,
        "parent_name": "背板配置"
    }}
    ```
    現在請解析以上知識片段，並輸出 JSON：
    """
    return prompt_template

def run_llm_classification_module():
    """
    執行 LLM 初步分類與結構化模塊。
    """
    logger.info("--- 啟動「LLM 初步分類與結構化」模塊 ---")

    df_manual_data = load_manual_data(TEMP_MANUAL_DATA_PATH)
    if df_manual_data.empty:
        logger.warning("沒有載入任何說明書數據，模塊停止執行。")
        df_empty = pd.DataFrame(columns=df_manual_data.columns) # 創建一個帶有相同列的空 DataFrame
        df_empty.to_excel(LLM_PROCESSED_DATA_PATH, index=False)
        logger.info(f"LLM 處理後的數據 (無新增處理) 已保存至: {LLM_PROCESSED_DATA_PATH}")
        logger.info("--- 「LLM 初步分類與結構化」模塊執行完畢 ---")
        return

    # 只處理 SourceType 為 '說明書文本' 或 '說明書表格' 或 '說明書圖片'，且 ExtractedContent 非空，
    # 且 '人工審核狀態' 不是 '已確認' 或 '已修正' 的記錄
    records_to_process_mask = (df_manual_data['SourceType'].isin(['說明書', '說明書文本', '說明書表格', '說明書圖片', '說明書原始表格'])) & \
                              (df_manual_data['ExtractedContent'].astype(bool)) & \
                              (~df_manual_data['人工審核狀態'].isin(['已確認', '已修正']))

    records_to_process_indices = df_manual_data[records_to_process_mask].index.tolist()
    
    if not records_to_process_indices:
        logger.info("沒有需要 LLM 處理的說明書記錄 (所有相關記錄皆已處理或已確認/修正)。")
        df_manual_data.to_excel(LLM_PROCESSED_DATA_PATH, index=False)
        logger.info(f"LLM 處理後的數據 (無新增處理) 已保存至: {LLM_PROCESSED_DATA_PATH}")
        logger.info("--- 「LLM 初步分類與結構化」模塊執行完畢 ---")
        return

    logger.info(f"找到 {len(records_to_process_indices)} 筆待 LLM 處理的說明書記錄。")

    # 使用 tqdm 顯示進度條，遍歷索引
    for i, index in enumerate(tqdm(records_to_process_indices, desc="LLM 處理進度")):
        row = df_manual_data.loc[index]
        extracted_content = row['ExtractedContent']
        content_type = row['ContentType']
        associated_product_name = row['AssociatedProductName']
        page_number = row['PageNumber'] 
        original_font_size = row['OriginalFontSize'] # 這些欄位已確保被load_manual_data處理為正確類型
        is_bold = row['IsBold']
        original_bbox = row['OriginalBBox']

        if not extracted_content.strip(): 
            logger.info(f"記錄 {index} (產品: {associated_product_name}, 類型: {content_type}) 內容為空，跳過 LLM 處理。")
            continue

        prompt = generate_prompt(content_type, extracted_content, associated_product_name, STANDARD_CATEGORIES, page_number, original_font_size, is_bold, original_bbox)
        
        if prompt is None:
            logger.error(f"記錄 {index} (產品: {associated_product_name}, 類型: {content_type}) 生成 Prompt 失敗，跳過 LLM 處理。")
            continue 

        # 這裡不打印完整 Prompt，避免過長，只打印部分或關鍵信息
        logger.debug(f"生成的 Prompt (部分): {prompt[:200]}...") 

        llm_response_text = call_ollama_api(prompt, OLLAMA_MODEL_NAME, OLLAMA_API_URL)

        if llm_response_text:
            parsed_result = parse_llm_response(llm_response_text)
            if parsed_result:
                df_manual_data.loc[index, 'PotentialItemName'] = parsed_result.get('item_name', '')
                df_manual_data.loc[index, 'PotentialCategory'] = parsed_result.get('category', '')
                df_manual_data.loc[index, 'ExtractedDescription'] = parsed_result.get('description', '')
                df_manual_data.loc[index, 'SuggestedLevel'] = parsed_result.get('level', '')
                df_manual_data.loc[index, 'SuggestedParentName'] = parsed_result.get('parent_name', '')
                df_manual_data.loc[index, 'LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                df_manual_data.loc[index, 'ManualReviewStatus'] = '待審核'

                logger.info(f"成功處理記錄 {index} (產品: {associated_product_name}, 類型: {content_type}) - 項目: {parsed_result.get('item_name', 'N/A')}")
            else:
                logger.warning(f"記錄 {index} (產品: {associated_product_name}, 類型: {content_type}) LLM 解析失敗或返回格式不正確。")
        else:
            logger.error(f"記錄 {index} (產品: {associated_product_name}, 類型: {content_type}) LLM 調用失敗，未收到響應。")
        
        time.sleep(0.1)

    df_manual_data.to_excel(LLM_PROCESSED_DATA_PATH, index=False)
    logger.info(f"LLM 處理後的數據已保存至: {LLM_PROCESSED_DATA_PATH}")
    logger.info("--- 「LLM 初步分類與結構化」模塊執行完畢 ---")

# --- 執行模塊 ---
if __name__ == "__main__":
    run_llm_classification_module()