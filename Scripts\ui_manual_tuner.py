# Ark_Knowledge_Engine/Scripts/ui_manual_tuner.py

import streamlit as st
import pandas as pd
import os
import logging
from datetime import datetime

# --- 配置區 ---
# 現在我們將載入 LLM 處理後的數據文件
TEMP_LLM_PROCESSED_DATA_PATH = os.path.join('..', 'Temp_Processed_Data', 'temp_llm_processed_manual_data.xlsx')
EXTRACTED_IMAGES_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'Temp_Processed_Data', 'Extracted_Images'))
# PDF 頁面預覽的基準目錄
PDF_PAGE_PREVIEWS_BASE_DIR = os.path.join(EXTRACTED_IMAGES_BASE_DIR, "Page_Previews")

LOG_DIR = os.path.join('..', 'Logs')
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, 'ui_manual_tuner.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UIManualTuner')

# 定義標準化的功能分類選項
CATEGORY_OPTIONS = ["硬體規格", "軟體功能", "操作流程", "系統設定", "故障排除", "配件說明", "服務信息", "未分類",
                    "產品特色說明", "產品功能簡介", "接口說明", "規格說明", "性能參數", "連接方式", "安全須知", "維護與保養", "圖像/多媒體", "表格數據", "原始表格", "版權聲明", "安全提示"]
REVIEW_STATUS_OPTIONS = ["待審核", "已確認", "需修正"]

# --- 定義期望的欄位作為全局常量 ---
EXPECTED_COLS_FOR_UI = [
    'SourceType', 'SourceFile', 'AssociatedProductName', 'OriginalID', 
    'ExtractedRawText', 'ExtractedImagePaths', 'PdfPageImagePaths', 
    'ExtractedContent', 
    'ContentType', 
    'PageNumber', 
    'OriginalFontSize', 'IsBold', 'OriginalBBox', # 確保 IsBold 欄位存在
    'ExtractedDescription', 
    'PotentialItemName', 'PotentialCategory', 'SuggestedLevel', 'SuggestedParentName', 
    'ManualReviewFlag', 'ManualReviewNotes', 'BOMLink', 'ManualLink', 'LastProcessedDate',
    'BOMID', 'PartNumberID_BOM', 'PartName_PartLib', 'Specification_PartLib', 
    'Location_BOM', 'PartCategory_PartLib', 'Quantity_BOM', 'Unit_BOM',
    'BOMType_BOM', 'Version_BOM', 'EffectiveDate_BOM', 'OriginalNotes',
    '問題追蹤ID', '問題狀態', '問題描述_問題追蹤', 
    '人工審核狀態', '人工修正建議'
]


# --- 數據載入與保存函數 ---

@st.cache_data # 使用 Streamlit 緩存數據，提高性能
def load_data(file_path):
    """載入 LLM 處理後的數據。"""
    if os.path.exists(file_path):
        logger.info(f"正在載入數據: {file_path}")
        df = pd.read_excel(file_path)
        
        # 填充和類型轉換邏輯
        for col in EXPECTED_COLS_FOR_UI: 
            if col not in df.columns:
                df[col] = '' 
            else:
                df[col] = df[col].fillna('') 

        # 確保特定欄位是數字類型
        for col in ['PageNumber', 'OriginalFontSize']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
        
        # 確保 IsBold 是布林值
        if 'IsBold' in df.columns:
            df['IsBold'] = df['IsBold'].apply(lambda x: str(x).lower() == 'true')


        # 確保其他顯示和編輯欄位是字符串類型
        # 排除數字和布林類型，避免轉為str後無法判斷
        cols_to_str = [col for col in EXPECTED_COLS_FOR_UI if col not in ['PageNumber', 'OriginalFontSize', 'IsBold']] 
        for col in cols_to_str:
            if col in df.columns: 
                df[col] = df[col].astype(str)
        
        logger.info(f"數據載入成功，共 {len(df)} 筆記錄。")
        return df
    else:
        logger.error(f"錯誤: 數據檔案不存在: {file_path}。請先運行 llm_classify_manual_data.py。")
        st.error(f"錯誤: 數據檔案不存在。請先運行 `llm_classify_manual_data.py` 生成 `{os.path.basename(file_path)}`。")
        return pd.DataFrame(columns=EXPECTED_COLS_FOR_UI)

def save_data(df, file_path):
    """保存數據到 Excel 檔案。"""
    try:
        df.to_excel(file_path, index=False)
        logger.info(f"數據已成功保存至: {file_path}")
        st.success("數據保存成功！")
    except Exception as e:
        logger.error(f"錯誤: 保存數據失敗到 '{file_path}': {e}", exc_info=True)
        st.error(f"保存數據失敗: {e}")

# --- Streamlit UI 介面 ---

def main():
    st.set_page_config(layout="wide", page_title="方舟知識引擎 - 說明書調教")
    
    # --- 頂部導航欄 ---
    header_col1, header_col2 = st.columns([0.8, 0.2])
    with header_col1:
        st.title("📖 方舟知識引擎 - 說明書數據調教平台")
    with header_col2:
        st.markdown(
            """
            <div style="display: flex; justify-content: flex-end; align-items: center; height: 100%;">
                <h2 style="margin: 0; color: #4CAF50;">方舟知識引擎</h2>
            </div>
            """, unsafe_allow_html=True
        )
    
    tab_cols = st.columns(5) # 5個導航選項
    tab_names = ["硬體(HW)", "軟體(SW)", "功能操作", "系統設定", "知識庫"]
    
    if 'active_tab' not in st.session_state:
        st.session_state.active_tab = "硬體(HW)"

    for i, tab_name in enumerate(tab_names):
        with tab_cols[i]:
            if st.button(tab_name, use_container_width=True, key=f"tab_button_{tab_name}"):
                st.session_state.active_tab = tab_name
                st.rerun()

    st.info(f"當前激活選項: **{st.session_state.active_tab}** (此處為UI展示，實際篩選功能將在後續實現)")

    st.markdown("---") 

    # 載入數據 (現在載入 LLM 處理後的數據)
    df = load_data(TEMP_LLM_PROCESSED_DATA_PATH)

    if df.empty:
        st.stop()

    if 'current_index' not in st.session_state:
        st.session_state.current_index = 0
    if 'df_data' not in st.session_state:
        st.session_state.df_data = df.copy() 
    if 'selected_product_prev' not in st.session_state:
        st.session_state.selected_product_prev = '所有產品'

    # --- 左側導航欄 (Sidebar) ---
    with st.sidebar:
        st.header("導航與篩選")
        
        # 產品篩選
        filtered_for_product_selection = st.session_state.df_data[st.session_state.df_data['SourceType'].str.contains('說明書')]
        unique_products = ['所有產品'] + filtered_for_product_selection['AssociatedProductName'].unique().tolist()
        selected_product = st.selectbox("選擇產品", unique_products, key='product_selector')

        # 過濾數據集 (現在考慮 SourceType 篩選)
        current_data_for_display = st.session_state.df_data[
            st.session_state.df_data['SourceType'].isin(['說明書', '說明書文本', '說明書表格', '說明書圖片', '說明書原始表格']) # SourceType現在統一為'說明書'
        ]
        if selected_product != '所有產品':
            current_data_for_display = current_data_for_display[current_data_for_display['AssociatedProductName'] == selected_product]
        
        filtered_df_indices = current_data_for_display.index.tolist()

        if st.session_state.selected_product_prev != selected_product or 'filtered_indices' not in st.session_state or st.session_state.filtered_indices != filtered_df_indices:
            st.session_state.current_index = 0
            st.session_state.selected_product_prev = selected_product
            st.session_state.filtered_indices = filtered_df_indices
            st.rerun()

        if not st.session_state.filtered_indices:
            st.warning("當前篩選條件下沒有記錄。")
            st.stop()

        total_filtered_records = len(st.session_state.filtered_indices)
        current_display_index = st.session_state.current_index 
        
        st.markdown(f"<p style='text-align: center;'>**記錄 {current_display_index + 1} / {total_filtered_records}**</p>", unsafe_allow_html=True)
        if st.session_state.filtered_indices:
            actual_df_index = st.session_state.filtered_indices[current_display_index]
            current_record_actual_id = st.session_state.df_data.loc[actual_df_index, 'OriginalID']
            st.markdown(f"<p style='text-align: center;'>原始ID: `{current_record_actual_id}`</p>", unsafe_allow_html=True)
        else:
            st.markdown("<p style='text-align: center;'>原始ID: `-`</p>", unsafe_allow_html=True)

        col1_nav, col2_nav = st.columns(2)
        with col1_nav:
            if st.button("上一條", use_container_width=True):
                if st.session_state.current_index > 0:
                    st.session_state.current_index -= 1
                    st.rerun()
                else:
                    st.info("這已是第一條記錄。")
        with col2_nav:
            if st.button("下一條", use_container_width=True):
                if st.session_state.current_index < total_filtered_records - 1:
                    st.session_state.current_index += 1
                    st.rerun()
                else:
                    st.info("這已是最後一條記錄。")
        
        st.markdown("---")
        st.subheader("最近日誌")
        try:
            with open(LOG_FILE, 'r', encoding='utf-8') as f:
                log_content = f.read().splitlines()
                st.text_area("日誌輸出", "\n".join(log_content[-15:]), height=200, disabled=True)
        except FileNotFoundError:
            st.info("日誌檔案尚未生成。")


    # --- 主內容區 (OmniParse 風格佈局) ---
    if st.session_state.filtered_indices:
        current_df_actual_index = st.session_state.filtered_indices[st.session_state.current_index]
        current_record = st.session_state.df_data.loc[current_df_actual_index]

        main_col_left, main_col_right = st.columns([0.5, 0.5]) # 左右各佔一半

        with main_col_left:
            st.subheader(f"來源文件: {current_record['SourceFile']}")
            st.markdown(f"**產品:** `{current_record['AssociatedProductName']}`")
            st.markdown(f"**頁碼:** `{current_record['PageNumber']}` | **內容類型:** `{current_record['ContentType']}`")
            st.markdown(f"**原始字體大小:** `{current_record['OriginalFontSize']}` | **是否加粗:** `{current_record['IsBold']}`")
            st.markdown(f"**原始邊界框:** `{current_record['OriginalBBox']}`")


            st.markdown("### 原始數據預覽")
            
            # --- PDF 頁面預覽 ---
            pdf_page_image_paths_str = current_record['PdfPageImagePaths']
            if pdf_page_image_paths_str and pdf_page_image_paths_str != 'nan': 
                pdf_page_paths = pdf_page_image_paths_str.split(";")
                if pdf_page_paths:
                    target_page_preview_path = None
                    current_page_num = int(current_record['PageNumber']) if str(current_record['PageNumber']).isdigit() else 1
                    
                    for p_path in pdf_page_paths:
                        if f"_Page{current_page_num}.png" in p_path:
                            target_page_preview_path = p_path
                            break
                    
                    if target_page_preview_path:
                        full_preview_path = os.path.join(EXTRACTED_IMAGES_BASE_DIR, target_page_preview_path)
                        if os.path.exists(full_preview_path):
                            st.image(full_preview_path, caption=f"頁面預覽 ({os.path.basename(target_page_preview_path)})", use_column_width=True)
                            logger.info(f"UI 顯示 PDF 頁面預覽: {full_preview_path}")
                        else:
                            st.warning(f"PDF 頁面預覽圖片檔案不存在或路徑錯誤: {full_preview_path}")
                            logger.warning(f"UI 顯示失敗: PDF 頁面預覽圖片檔案不存在: {full_preview_path}")
                    elif pdf_page_paths: 
                        full_preview_path = os.path.join(EXTRACTED_IMAGES_BASE_DIR, pdf_page_paths[0])
                        if os.path.exists(full_preview_path):
                            st.image(full_preview_path, caption=f"頁面預覽 (第一頁) ({os.path.basename(pdf_page_paths[0])})", use_column_width=True)
                            logger.info(f"UI 顯示 PDF 頁面預覽 (fallback): {full_preview_path}")
                        else:
                            st.info("無 PDF 頁面預覽圖路徑。")
                else:
                    st.info("無 PDF 頁面預覽圖路徑。")
            else:
                st.info("非 PDF 文件或無 PDF 頁面預覽圖。")

            st.text_area("提取內容 (ExtractedContent)", value=current_record['ExtractedContent'], height=200, disabled=True, key=f"extracted_content_{current_df_actual_index}")
            
            with st.expander("原始文本 (ExtractedRawText)", expanded=False):
                st.text_area("", value=current_record['ExtractedRawText'], height=150, disabled=True, key=f"raw_text_full_{current_df_actual_index}")

            st.markdown("---")
            st.markdown("### 相關圖片")
            extracted_image_paths_str = current_record['RelatedImagePaths'] 
            if extracted_image_paths_str and extracted_image_paths_str != 'nan': 
                extracted_images = extracted_image_paths_str.split(";")
                if extracted_images:
                    for img_rel_path in extracted_images:
                        full_img_path = os.path.join(EXTRACTED_IMAGES_BASE_DIR, img_rel_path)
                        if os.path.exists(full_img_path):
                            st.image(full_img_path, caption=os.path.basename(img_rel_path), use_column_width=True)
                            logger.info(f"UI 顯示內嵌圖片: {full_img_path}")
                        else:
                            st.warning(f"內嵌圖片檔案不存在或路徑錯誤: {full_img_path}")
                            logger.warning(f"UI 顯示失敗: 內嵌圖片檔案不存在: {full_img_path}")
                else:
                    st.info("無相關提取圖片路徑。")
            else:
                st.info("無相關提取圖片。")


        with main_col_right:
            st.subheader("數據調教與編輯")

            with st.expander("核心功能調教 (AI 初步結果與人工修正)", expanded=True):
                st.markdown("---")
                st.markdown(f"**AI 建議結果 (來自 `{current_record['LastProcessedDate']}`):**") 
                st.markdown(f"- 項目名稱: `{current_record['PotentialItemName']}`" if current_record['PotentialItemName'] else "AI 建議項目名稱: -")
                st.markdown(f"- 分類: `{current_record['PotentialCategory']}`" if current_record['PotentialCategory'] else "AI 建議分類: -")
                st.markdown(f"- 描述: `{current_record['ExtractedDescription']}`" if current_record['ExtractedDescription'] else "AI 建議描述: -")
                st.markdown(f"- 層級: `{current_record['SuggestedLevel']}`" if current_record['SuggestedLevel'] else "AI 建議層級: -")
                st.markdown(f"- 父級名稱: `{current_record['SuggestedParentName']}`" if current_record['SuggestedParentName'] else "AI 建議父級名稱: -")
                st.markdown("---") 

                potential_item_name_manual = st.text_input(
                    "項目名稱 (人工修正/確認)", 
                    value=current_record['PotentialItemName'], 
                    key=f"item_name_manual_{current_df_actual_index}"
                )
                
                current_category_idx = 0
                if current_record['PotentialCategory'] in CATEGORY_OPTIONS:
                    current_category_idx = CATEGORY_OPTIONS.index(current_record['PotentialCategory'])
                else:
                    if current_record['PotentialCategory'] not in ['', 'nan']: 
                         logger.warning(f"LLM 輸出的分類 '{current_record['PotentialCategory']}' 不在預設選項中。")
                         # 為了確保選中，將其添加到當前會話的選項中，但不會改變全局 CATEGORY_OPTIONS
                         # 這需要更複雜的狀態管理，這裡簡化為如果不在選項中，則選擇默認值或日誌警告。
                         # 更穩健的處理：讓 selectbox 的 index 指向 0 (未分類)
                         pass # 保持 index 為 0

                potential_category_manual = st.selectbox(
                    "分類 (人工修正/確認)", 
                    options=CATEGORY_OPTIONS, 
                    index=current_category_idx,
                    key=f"category_manual_{current_df_actual_index}"
                )
                
                extracted_description_manual = st.text_area(
                    "詳細描述 (人工編輯/精煉)", 
                    value=current_record['ExtractedDescription'], 
                    height=150, 
                    key=f"description_manual_{current_df_actual_index}"
                )
                
                col_level_parent = st.columns(2)
                with col_level_parent[0]:
                    suggested_level_manual = st.text_input(
                        "建議層級 (人工修正)", 
                        value=current_record['SuggestedLevel'], 
                        key=f"level_manual_{current_df_actual_index}"
                    )
                with col_level_parent[1]:
                    suggested_parent_name_manual = st.text_input(
                        "建議父級名稱 (人工修正)", 
                        value=current_record['SuggestedParentName'], 
                        key=f"parent_name_manual_{current_df_actual_index}"
                    )

            with st.expander("原始數據元信息 (只讀)", expanded=False): 
                st.markdown(f"**來源類型:** `{current_record['SourceType']}`")
                st.markdown(f"**原始字體大小:** `{current_record['OriginalFontSize']}`")
                st.markdown(f"**是否加粗:** `{current_record['IsBold']}`")
                st.markdown(f"**原始邊界框:** `{current_record['OriginalBBox']}`")
                st.markdown(f"**最後處理日期:** `{current_record['LastProcessedDate']}`")


            with st.expander("產品總覽數據 (可編輯)", expanded=False):
                st.markdown(f"**產品ID (總覽):** `{current_record['OriginalID']}`") 
                # 這些欄位的值需要從 session_state 中獲取，並提供默認值
                st.text_input("產品型態", value=current_record.get('ProductType', ''), key=f"product_type_{current_df_actual_index}")
                st.text_input("產品狀態", value=current_record.get('ProductStatus', ''), key=f"product_status_{current_df_actual_index}")
                st.text_input("BOMLink", value=current_record.get('BOMLink', ''), key=f"bom_link_{current_df_actual_index}")
                st.text_input("ManualLink", value=current_record.get('ManualLink', ''), key=f"manual_link_{current_df_actual_index}")
                st.text_area("產品總覽備註", value=current_record.get('OriginalNotes', ''), height=80, key=f"notes_prod_overview_{current_df_actual_index}")


            with st.expander("BOM 關聯數據 (可編輯)", expanded=False):
                st.text_input("BOMID", value=current_record.get('BOMID', ''), disabled=True, key=f"bom_id_{current_df_actual_index}")
                st.text_input("零件料號 (BOM)", value=current_record.get('PartNumberID_BOM', ''), key=f"part_num_bom_{current_df_actual_index}")
                st.text_input("零件名稱 (庫)", value=current_record.get('PartName_PartLib', ''), key=f"part_name_lib_{current_df_actual_index}")
                st.text_area("規格描述 (庫)", value=current_record.get('Specification_PartLib', ''), height=80, key=f"spec_lib_{current_df_actual_index}")
                st.text_input("位置 (BOM)", value=current_record.get('Location_BOM', ''), key=f"location_bom_{current_df_actual_index}")
                st.text_input("數量 (BOM)", value=current_record.get('Quantity_BOM', ''), key=f"qty_bom_{current_df_actual_index}")
                st.text_input("單位 (BOM)", value=current_record.get('Unit_BOM', ''), key=f"unit_bom_{current_df_actual_index}")
                st.text_input("BOM類型", value=current_record.get('BOMType_BOM', ''), key=f"bom_type_{current_df_actual_index}")
                st.text_input("版本 (BOM)", value=current_record.get('Version_BOM', ''), key=f"version_bom_{current_df_actual_index}")
                st.text_input("生效日期 (BOM)", value=current_record.get('EffectiveDate_BOM', ''), key=f"eff_date_bom_{current_df_actual_index}")


            with st.expander("問題追蹤數據 (可編輯)", expanded=False):
                st.text_input("問題追蹤ID", value=current_record.get('問題追蹤ID', ''), key=f"issue_id_{current_df_actual_index}")
                st.text_input("問題狀態", value=current_record.get('問題狀態', ''), key=f"issue_status_{current_df_actual_index}")
                st.text_area("問題描述", value=current_record.get('問題描述_問題追蹤', ''), height=80, key=f"issue_desc_{current_df_actual_index}")


            with st.expander("審核狀態與備註", expanded=True):
                current_status_idx = REVIEW_STATUS_OPTIONS.index(current_record['人工審核狀態']) if current_record['人工審核狀態'] in REVIEW_STATUS_OPTIONS else 0
                review_status = st.selectbox(
                    "人工審核狀態", 
                    options=REVIEW_STATUS_OPTIONS, 
                    index=current_status_idx,
                    key=f"status_{current_df_actual_index}"
                )

                manual_correction_notes = st.text_area(
                    "人工修正建議/備註", 
                    value=current_record['ManualCorrectionNotes'], 
                    height=100, 
                    key=f"notes_manual_correction_{current_df_actual_index}"
                )


            st.markdown("---")
            if st.button("保存當前調教數據", use_container_width=True):
                # 更新 DataFrame 的副本，使用變量名而非直接從控件讀取，確保一致性
                st.session_state.df_data.loc[current_df_actual_index, 'PotentialItemName'] = potential_item_name_manual
                st.session_state.df_data.loc[current_df_actual_index, 'PotentialCategory'] = potential_category_manual
                st.session_state.df_data.loc[current_df_actual_index, 'ExtractedDescription'] = extracted_description_manual
                st.session_state.df_data.loc[current_df_actual_index, 'SuggestedLevel'] = suggested_level_manual
                st.session_state.df_data.loc[current_df_actual_index, 'SuggestedParentName'] = suggested_parent_name_manual
                st.session_state.df_data.loc[current_df_actual_index, 'ManualReviewStatus'] = review_status
                st.session_state.df_data.loc[current_df_actual_index, 'ManualCorrectionNotes'] = manual_correction_notes
                st.session_state.df_data.loc[current_df_actual_index, 'LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 更新其他可編輯的欄位，直接從 Streamlit 的 session_state 讀取控件的值
                # 使用 get() 確保鍵存在，避免錯誤
                st.session_state.df_data.loc[current_df_actual_index, 'ProductType'] = st.session_state.get(f"product_type_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'ProductStatus'] = st.session_state.get(f"product_status_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'BOMLink'] = st.session_state.get(f"bom_link_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'ManualLink'] = st.session_state.get(f"manual_link_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'OriginalNotes'] = st.session_state.get(f"notes_prod_overview_{current_df_actual_index}", "") 


                st.session_state.df_data.loc[current_df_actual_index, 'PartNumberID_BOM'] = st.session_state.get(f"part_num_bom_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'PartName_PartLib'] = st.session_state.get(f"part_name_lib_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'Specification_PartLib'] = st.session_state.get(f"spec_lib_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'Location_BOM'] = st.session_state.get(f"location_bom_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'Quantity_BOM'] = st.session_state.get(f"qty_bom_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'Unit_BOM'] = st.session_state.get(f"unit_bom_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'BOMType_BOM'] = st.session_state.get(f"bom_type_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'Version_BOM'] = st.session_state.get(f"version_bom_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, 'EffectiveDate_BOM'] = st.session_state.get(f"eff_date_bom_{current_df_actual_index}", "")

                st.session_state.df_data.loc[current_df_actual_index, '問題追蹤ID'] = st.session_state.get(f"issue_id_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, '問題狀態'] = st.session_state.get(f"issue_status_{current_df_actual_index}", "")
                st.session_state.df_data.loc[current_df_actual_index, '問題描述_問題追蹤'] = st.session_state.get(f"issue_desc_{current_df_actual_index}", "")


                save_data(st.session_state.df_data, TEMP_LLM_PROCESSED_DATA_PATH)


if __name__ == "__main__":
    main()