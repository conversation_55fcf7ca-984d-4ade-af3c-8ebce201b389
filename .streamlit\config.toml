```toml
[browser]
gatherUsageStats = false
[server]
enableCORS = false
enableXsrfProtection = false

[client]
toolbarMode = "minimal" # 顯示最小化的工具欄，或者
# toolbarMode = "off" # 完全關閉工具欄 (可能導致一些內置功能也無法使用，不推薦)

[global]
disableWatchdog = true # 禁用文件監控（在某些環境下可能導致性能問題或不必要行為）

[runner]
# 可能需要針對特定版本禁用這些外部連結，但通常 toolbarMode 就能解決
# 這部分沒有直接的配置項來禁用「詢問谷歌/ChatGPT」，只能通過禁用部分功能或尋求社區解決方案。
# 這裡我們主要依賴 toolbarMode='minimal' 來減少其可見性。
```

**注意：** 嚴格禁用「詢問谷歌/ChatGPT」這種內置的上下文菜單選項，Streamlit 目前**沒有直接的配置項**可以做到。`toolbarMode = "minimal"` 或 `hidden` 可以減少工具欄的顯示，從而減少這些選項的出現。最徹底的方法可能涉及到前端 JavaScript 注入，但這會增加複雜度。**我們主要依賴開發者在設計 UI 時避免這些功能點，並告知用戶不要使用。**