# Ark_Knowledge_Engine/Scripts/extract_manual_data.py

import pandas as pd
import os
import uuid
from datetime import datetime
import re
import logging
import json 

# --- 配置區 ---
MANUALS_DIR = os.path.join('..', 'Raw_Data_Sources', 'Product_Manuals')
TEMP_OUTPUT_DIR = os.path.join('..', 'Temp_Processed_Data')
EXTRACTED_IMAGES_BASE_DIR = os.path.join(TEMP_OUTPUT_DIR, 'Extracted_Images') 
PDF_PAGE_PREVIEWS_DIR_NAME = "Page_Previews" 
EXTRACTED_PDF_PAGE_PREVIEWS_BASE_DIR = os.path.join(EXTRACTED_IMAGES_BASE_DIR, PDF_PAGE_PREVIEWS_DIR_NAME)

# 確保輸出資料夾存在
os.makedirs(TEMP_OUTPUT_DIR, exist_ok=True)
os.makedirs(EXTRACTED_IMAGES_BASE_DIR, exist_ok=True)
os.makedirs(EXTRACTED_PDF_PAGE_PREVIEWS_BASE_DIR, exist_ok=True)

# --- 日誌配置 ---
LOG_DIR = os.path.join('..', 'Logs')
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, 'preprocessing.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ManualDataExtractor') 

fitz = None
try:
    import fitz 
    logger.info("PyMuPDF (fitz) 模組已載入。")
except ImportError:
    logger.warning("警告: PyMuPDF (fitz) 模組未安裝。PDF 檔案內容及圖片將無法提取。請執行 'pip install PyMuPDF'。")

Document = None
RT = None
BytesIO = None
try:
    from docx import Document 
    from docx.opc.constants import RELATIONSHIP_TYPE as RT 
    from docx.shared import Inches 
    from io import BytesIO
    logger.info("python-docx 模組已載入。")
except ImportError:
    logger.warning("警告: python-docx 模組未安裝。DOCX 檔案內容將無法提取。請執行 'pip install python-docx'。")

DUMMY_PRODUCT_OVERVIEW_DF = pd.DataFrame(columns=['ProductName', 'BOMLink', 'ManualLink'])


# --- 輔助函數 ---

def get_product_links(product_name, df_product_overview):
    """從產品總覽中獲取 BOMLink 和 ManualLink。"""
    product_overview_names = df_product_overview['ProductName'].astype(str)
    if product_name in product_overview_names.values:
        product_info = df_product_overview[product_overview_names == product_name].iloc[0]
        return product_info.get('BOMLink', ''), product_info.get('ManualLink', '')
    return '', ''

def _extract_text_blocks_from_pdf(page):
    """
    從 PDF 頁面提取文本塊，嘗試判斷其類型和層次。
    返回一個列表，每個元素是字典 {'type': 'header/paragraph/list', 'content': '文本', 'font_size': X, 'bbox': (x0,y0,x1,y1)}
    """
    page_elements = [] 
    if not fitz: 
        logger.error("PyMuPDF (fitz) 模組未載入，無法提取 PDF 文本塊。")
        return page_elements 

    blocks = page.get_text("dict")["blocks"]
    
    base_font_size = None
    try:
        all_font_sizes = [span['size'] for b in blocks for l in b['lines'] for span in l['spans'] if 'spans' in l and l['spans']] 
        if all_font_sizes:
            base_font_size = pd.Series(all_font_sizes).mode()[0]
    except Exception as e:
        logger.debug(f"無法估計PDF頁面基準字體大小: {e}")

    current_header_stack = [] 

    for b_idx, b in enumerate(blocks):
        if b['type'] == 0: 
            for l_idx, line in enumerate(b['lines']):
                if not line['spans']: 
                    continue
                
                line_text = "".join([span['text'] for span in line['spans']]).strip()
                if not line_text: continue
                
                first_span = line['spans'][0]
                font_size = first_span['size']
                is_bold = ("bold" in first_span.get('font', '').lower()) if 'font' in first_span else False 
                
                bbox = fitz.Rect(line['bbox']) if fitz and hasattr(fitz, 'Rect') else None 
                if bbox is None:
                    logger.warning(f"警告: 無法獲取文本行的邊界框，跳過此行。頁碼: {page.number+1}, 行索引: {l_idx}")
                    continue

                content_type = 'paragraph'
                
                if base_font_size is not None and font_size > base_font_size * 1.25 and is_bold and len(line_text.splitlines()) == 1:
                    content_type = 'header_h2' 
                    if font_size > base_font_size * 1.5:
                        content_type = 'header_h1' 
                elif base_font_size is not None and font_size > base_font_size * 1.1 and len(line_text) < 100:
                    content_type = 'header_h3' 

                if re.match(r'^\s*([0-9a-zA-Z]+\.|\-|\*|•|·)\s+', line_text) and (bbox.x0 if bbox else 0) > page.rect.x0 + 20: 
                     content_type = 'list_item'
                
                if re.search(r'表\s*\d+\.\d+|Table\s*\d+\.\d+', line_text, re.IGNORECASE):
                    content_type = 'table_caption_text' 

                parent_id = None
                parent_name = None
                if content_type.startswith('header'):
                    level_num = int(content_type[-1]) 
                    while current_header_stack and current_header_stack[-1]['level'] >= level_num:
                        current_header_stack.pop()
                    current_header_stack.append({'level': level_num, 'id': f"{page.number+1}_{b_idx}_{l_idx}", 'content': line_text})
                
                if current_header_stack:
                    parent_id = current_header_stack[-1]['id']
                    parent_name = current_header_stack[-1]['content']

                page_elements.append({
                    'id_suffix': f"T_{b_idx}_{l_idx}", 
                    'ContentType': content_type,
                    'content': line_text, 
                    'font_size': font_size,
                    'is_bold': is_bold, 
                    'bbox': str(bbox.round()) if bbox else '', 
                    'SuggestedParentID_temp': parent_id, 
                    'SuggestedParentName_temp': parent_name,
                })
        elif b['type'] == 1: 
            pass 
    
    tables = page.find_tables()
    for t_idx, table in enumerate(tables):
        table_markdown = ""
        try:
            table_data = table.extract()
            if table_data and len(table_data) > 0:
                header = [str(cell).strip() for cell in table_data[0]] 
                table_markdown += "| " + " | ".join(header) + " |\n"
                table_markdown += "| " + " | ".join(["---"] * len(header)) + " |\n"
                for row_idx, row_cells in enumerate(table_data[1:]): 
                    cells_text = [str(cell).strip() for cell in row_cells]
                    table_markdown += "| " + " | ".join(cells_text) + " |\n"
            else:
                table_markdown = "表格內容為空或無法提取。"
            
            table_bbox = fitz.Rect(table.bbox).round() if fitz and hasattr(fitz, 'Rect') else None 

            if table_markdown.strip():
                page_elements.append({
                    'id_suffix': f"Table_{t_idx}",
                    'ContentType': 'table',
                    'content': table_markdown, 
                    'RelatedImagePaths': '', 
                    'font_size': '', 
                    'is_bold': '',
                    'bbox': str(table_bbox) if table_bbox else '',
                    'SuggestedParentID_temp': None, 
                    'SuggestedParentName_temp': None,
                })
        except Exception as e:
            logger.error(f"錯誤: 無法從 PDF 提取表格: {e}", exc_info=True)
    return page_elements


def extract_images_from_pdf(pdf_doc, page, product_specific_image_dir, output_base_dir, product_name):
    """
    從單個 PDF 頁面提取內嵌圖片，保存到指定資料夾，並返回圖片相對路徑列表。
    同時嘗試識別圖片的邊界框。
    """
    extracted_image_info_list = [] 
    
    os.makedirs(product_specific_image_dir, exist_ok=True)
    
    image_list = page.get_images(full=True)

    for img_index, img_info in enumerate(image_list):
        xref = img_info[0]
        try:
            base_image = pdf_doc.extract_image(xref) 
            image_bytes = base_image["image"]
            image_ext = base_image["ext"]

            if image_ext.lower() not in ['png', 'jpg', 'jpeg', 'gif', 'bmp']:
                image_ext = 'png' 

            image_filename = f"{product_name}_Page{page.number + 1}_Image{img_index + 1}.{image_ext}"
            image_full_path = os.path.join(product_specific_image_dir, image_filename)
            
            with open(image_full_path, "wb") as img_file:
                img_file.write(image_bytes)
            
            # 關鍵修改：統一分隔符，確保是相對於 EXTRACTED_IMAGES_BASE_DIR 的路徑
            relative_path = os.path.relpath(image_full_path, EXTRACTED_IMAGES_BASE_DIR).replace(os.sep, '/') 
            
            image_rect = None
            for img_obj in page.get_image_info(): 
                if 'xref' in img_obj and img_obj['xref'] == xref: 
                    image_rect = fitz.Rect(img_obj['bbox']) if fitz and hasattr(fitz, 'Rect') else None
                    break
            
            extracted_image_info_list.append({
                'path': relative_path, 
                'bbox': str(image_rect.round()) if image_rect else '' 
            })
            logger.info(f"提取內嵌圖片: {relative_path}")
        except Exception as e:
            logger.warning(f"警告: 無法從 PDF 頁面 {page.number+1} 提取圖片 {xref}: {e}", exc_info=True)
    return extracted_image_info_list

def extract_pdf_page_previews(pdf_doc, output_base_dir, product_name):
    """
    將 PDF 的每一頁轉換為圖片，保存為預覽圖，並返回相對於 EXTRACTED_IMAGES_BASE_DIR 的路徑列表。
    """
    if not fitz: 
        logger.error("PyMuPDF (fitz) 模組未載入，無法提取 PDF 頁面預覽。")
        return []
    page_preview_relative_paths = []
    
    product_page_preview_dir = os.path.join(output_base_dir, product_name)
    os.makedirs(product_page_preview_dir, exist_ok=True)

    for page_num in range(pdf_doc.page_count): 
        page = pdf_doc.load_page(page_num)
        pix = page.get_pixmap()
        
        preview_filename = f"{product_name}_Page{page_num + 1}.png"
        preview_full_path = os.path.join(product_page_preview_dir, preview_filename)
        
        try:
            pix.save(preview_full_path)
            # 關鍵修改：統一分隔符，確保是相對於 EXTRACTED_IMAGES_BASE_DIR 的路徑
            relative_path = os.path.relpath(preview_full_path, EXTRACTED_IMAGES_BASE_DIR).replace(os.sep, '/') 
            page_preview_relative_paths.append(relative_path)
            logger.info(f"提取 PDF 頁面預覽: {relative_path}")
        except Exception as e:
            logger.error(f"錯誤: 無法保存 PDF 頁面預覽從 '{pdf_doc.name}' 頁碼 {page_num + 1}: {e}", exc_info=True) 
    return page_preview_relative_paths


def _parse_pdf_document(file_path, product_name, extracted_images_base_dir, extracted_pdf_page_previews_base_dir):
    """
    解析 PDF 文檔，提取結構化文本塊、內嵌圖片和頁面預覽圖。
    返回一個包含多個知識片段的列表。
    """
    if not fitz: 
        logger.warning("PyMuPDF 模組未載入，無法解析 PDF 文檔。")
        return []
    
    doc = None 
    all_fragments_for_doc = []

    try:
        doc = fitz.open(file_path)
        if not doc:
            logger.error(f"錯誤: fitz.open 無法打開 PDF 文檔 '{file_path}'。")
            return []
        
        pdf_page_preview_paths = extract_pdf_page_previews(doc, extracted_pdf_page_previews_base_dir, product_name)
        
        for page_num in range(doc.page_count):
            page = doc.load_page(page_num)
            
            page_elements = _extract_text_blocks_from_pdf(page) 
            page_extracted_image_info = extract_images_from_pdf(doc, page, os.path.join(extracted_images_base_dir, product_name), extracted_images_base_dir, product_name) 
            
            for i, element in enumerate(page_elements):
                fragment_id = f"{product_name}_P{page_num+1}_{element['ContentType'].replace('header_H', 'H').upper()}_{i+1}"
                
                fragment = {
                    'SourceType': '說明書',
                    'SourceFile': os.path.basename(file_path),
                    'AssociatedProductName': product_name,
                    'OriginalID': fragment_id, 
                    'PageNumber': page_num + 1,
                    'ContentType': element['ContentType'],
                    'ExtractedContent': element['content'], 
                    'OriginalFontSize': element.get('font_size', ''), 
                    'IsBold': element.get('is_bold', ''),
                    'OriginalBBox': element.get('bbox', ''),
                    'ExtractedRawText': element['content'], 
                    'RelatedImagePaths': '', 
                    'PdfPageImagePaths': "", 
                    'ExtractedDescription': '', 
                    'PotentialItemName': '',
                    'PotentialCategory': '',
                    'SuggestedLevel': '',
                    'SuggestedParentName': '',
                    'ManualReviewFlag': True,
                    'ManualReviewNotes': '',
                    'LastProcessedDate': ''
                }
                all_fragments_for_doc.append(fragment)

            for i, img_info in enumerate(page_extracted_image_info):
                fragment_id = f"{product_name}_P{page_num+1}_IMG_{i+1}"
                all_fragments_for_doc.append({
                    'SourceType': '說明書',
                    'SourceFile': os.path.basename(file_path),
                    'AssociatedProductName': product_name,
                    'OriginalID': fragment_id,
                    'PageNumber': page_num + 1,
                    'ContentType': 'image',
                    'ExtractedContent': f"圖片文件: {os.path.basename(img_info['path'])}", 
                    'RelatedImagePaths': img_info['path'], 
                    'PdfPageImagePaths': "", 
                    'OriginalFontSize': '', 
                    'IsBold': '',
                    'OriginalBBox': img_info['bbox'], 
                    'ExtractedRawText': f"圖片文件: {os.path.basename(img_info['path'])}",
                    'ExtractedDescription': '', 
                    'PotentialItemName': '',
                    'PotentialCategory': '圖像/多媒體', 
                    'SuggestedLevel': '',
                    'SuggestedParentName': '',
                    'ManualReviewFlag': True,
                    'ManualReviewNotes': '',
                    'LastProcessedDate': ''
                })
        
        if all_fragments_for_doc and pdf_page_preview_paths:
            all_fragments_for_doc[0]['PdfPageImagePaths'] = ";".join(pdf_page_preview_paths)

    except Exception as e:
        logger.error(f"錯誤: 無法解析 PDF 文檔 '{file_path}': {e}", exc_info=True)
    finally:
        if doc: 
            doc.close()
    return all_fragments_for_doc


def _parse_docx_document(file_path, product_name, extracted_images_base_dir):
    """
    解析 DOCX 文檔，提取結構化文本塊和圖片。
    返回一個包含多個知識片段的列表。
    """
    if not Document: 
        logger.warning("python-docx 模組未載入，無法解析 DOCX 文檔。")
        return []
    knowledge_fragments = []
    
    try:
        doc = Document(file_path)
        
        for i, paragraph in enumerate(doc.paragraphs):
            fragment_id = f"{product_name}_DocXPara{i+1}"
            content_type = 'paragraph'
            
            if paragraph.style and hasattr(paragraph.style, 'name') and isinstance(paragraph.style.name, str):
                if paragraph.style.name.startswith('Heading'):
                    content_type = f"header_H{paragraph.style.name.replace('Heading ', '')}"
            
            if re.match(r'^\s*([0-9a-zA-Z]+\.|\-|\*|•|·)\s+', paragraph.text.strip()):
                content_type = 'list_item'

            if paragraph.text.strip(): 
                knowledge_fragments.append({
                    'SourceType': '說明書',
                    'SourceFile': os.path.basename(file_path),
                    'AssociatedProductName': product_name,
                    'OriginalID': fragment_id,
                    'PageNumber': 0, 
                    'ContentType': content_type,
                    'ExtractedContent': paragraph.text.strip(),
                    'RelatedImagePaths': '', 
                    'PdfPageImagePaths': '', 
                    'OriginalFontSize': '', 
                    'IsBold': paragraph.runs[0].bold if paragraph.runs and len(paragraph.runs) > 0 and hasattr(paragraph.runs[0], 'bold') else False, 
                    'OriginalBBox': '', 
                    'ExtractedRawText': paragraph.text.strip(),
                    'ExtractedDescription': '', 
                    'PotentialItemName': '',
                    'PotentialCategory': '',
                    'SuggestedLevel': '',
                    'SuggestedParentName': '',
                    'ManualReviewFlag': True,
                    'ManualReviewNotes': '',
                    'LastProcessedDate': ''
                })
        
        for i, table in enumerate(doc.tables):
            fragment_id = f"{product_name}_DocXTable{i+1}"
            table_markdown = ""
            try:
                header = [str(cell.text).strip() for cell in table.rows[0].cells] 
                table_markdown += "| " + " | ".join(header) + " |\n"
                table_markdown += "| " + " | ".join(["---"] * len(header)) + " |\n"
                for row_idx, row in enumerate(table.rows):
                    if row_idx == 0: continue 
                    cells_text = [str(cell.text).strip() for cell in row.cells] 
                    table_markdown += "| " + " | ".join(cells_text) + " |\n"
            except Exception as e:
                table_markdown = f"無法轉換表格: {e}\n原始表格內容 (部分): " + "\n".join([" ".join([str(c.text) for c in r.cells]) for r in table.rows[:3]]) 

            if table_markdown.strip(): 
                knowledge_fragments.append({
                    'SourceType': '說明書',
                    'SourceFile': os.path.basename(file_path),
                    'AssociatedProductName': product_name,
                    'OriginalID': fragment_id,
                    'PageNumber': 0, 
                    'ContentType': 'table',
                    'ExtractedContent': table_markdown,
                    'RelatedImagePaths': '', 
                    'PdfPageImagePaths': '', 
                    'OriginalFontSize': '', 
                    'IsBold': '',
                    'OriginalBBox': '', 
                    'ExtractedRawText': table_markdown,
                    'ExtractedDescription': '', 
                    'PotentialItemName': '',
                    'PotentialCategory': '表格數據',
                    'SuggestedLevel': '',
                    'SuggestedParentName': '',
                    'ManualReviewFlag': True,
                    'ManualReviewNotes': '',
                    'LastProcessedDate': ''
                })
        
    except Exception as e:
        logger.error(f"錯誤: 無法解析 DOCX 文檔 '{file_path}': {e}", exc_info=True)
    return knowledge_fragments


def _parse_txt_document(file_path, product_name):
    """
    解析 TXT 文檔，提取為單個文本塊。
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content: return []
            
            fragment_id = f"{product_name}_TxtWhole"
            return [{
                'SourceType': '說明書',
                'SourceFile': os.path.basename(file_path),
                'AssociatedProductName': product_name,
                'OriginalID': fragment_id,
                'PageNumber': 0, 
                'ContentType': 'plain_text',
                'ExtractedContent': content,
                'RelatedImagePaths': '', 
                'PdfPageImagePaths': '', 
                'OriginalFontSize': '', 
                'IsBold': '',
                'OriginalBBox': '', 
                'ExtractedRawText': content,
                'ExtractedDescription': '', 
                'PotentialItemName': '',
                'PotentialCategory': '',
                'SuggestedLevel': '',
                'SuggestedParentName': '',
                'ManualReviewFlag': True,
                'ManualReviewNotes': '',
                'LastProcessedDate': ''
            }]
    except Exception as e:
        logger.error(f"錯誤: 無法提取 TXT 文本從 '{file_path}': {e}", exc_info=True)
        return []

def _parse_xlsx_document_as_raw_text(file_path, product_name):
    """
    將 XLSX 檔案內容作為原始文本提取。
    """
    try:
        xls = pd.ExcelFile(file_path)
        all_sheet_content = []
        for sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name)
            all_sheet_content.append(f"--- Sheet: {sheet_name} ---\n" + df.to_string(index=False))
        content = "\n\n".join(all_sheet_content).strip()
        if not content: return []

        fragment_id = f"{product_name}_XlsxRaw"
        return [{
            'SourceType': '說明書', 
            'SourceFile': os.path.basename(file_path),
            'AssociatedProductName': product_name,
            'OriginalID': fragment_id,
            'PageNumber': 0, 
            'ContentType': 'raw_table_text',
            'ExtractedContent': content,
            'RelatedImagePaths': '', 
            'PdfPageImagePaths': '', 
            'OriginalFontSize': '', 
            'IsBold': '',
            'OriginalBBox': '', 
            'ExtractedRawText': content,
            'ExtractedDescription': '', 
            'PotentialItemName': '',
            'PotentialCategory': '原始表格', 
            'SuggestedLevel': '',
            'SuggestedParentName': '',
            'ManualReviewFlag': True,
            'ManualReviewNotes': '',
            'LastProcessedDate': ''
        }]
    except Exception as e:
        logger.error(f"錯誤: 無法提取 XLSX 原始文本從 '{file_path}': {e}", exc_info=True)
        return []


def process_manual_data(manuals_dir, extracted_images_base_dir, extracted_pdf_page_previews_base_dir, df_product_overview):
    """
    遍歷說明書資料夾，讀取不同格式的檔案，提取文本和圖片信息，並初步結構化。
    現在返回的是多個知識片段組成的列表。
    """
    logger.info(f"正在處理說明書資料夾: {manuals_dir}...")
    all_knowledge_fragments = []

    for filename in os.listdir(manuals_dir):
        file_path = os.path.join(manuals_dir, filename)
        if os.path.isfile(file_path):
            base_name = os.path.splitext(filename)[0]
            product_name = str(base_name).replace("說明書", "").replace("遙控器", "").strip() 
            
            file_extension = os.path.splitext(filename)[1].lower()

            bo_link, manual_link = get_product_links(product_name, df_product_overview)

            logger.info(f"  - 處理檔案: {filename} (產品: {product_name})")
            
            current_file_fragments = [] 

            if file_extension == '.pdf':
                current_file_fragments = _parse_pdf_document(file_path, product_name, extracted_images_base_dir, extracted_pdf_page_previews_base_dir)
            elif file_extension == '.docx':
                current_file_fragments = _parse_docx_document(file_path, product_name, extracted_images_base_dir)
            elif file_extension == '.txt':
                current_file_fragments = _parse_txt_document(file_path, product_name)
            elif file_extension == '.xlsx':
                current_file_fragments = _parse_xlsx_document_as_raw_text(file_path, product_name)
            else:
                logger.warning(f"警告: 不支援的說明書檔案格式: {filename}")

            for fragment in current_file_fragments:
                fragment['BOMLink'] = bo_link
                fragment['ManualLink'] = manual_link
                fragment['LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                fragment['PotentialItemName'] = fragment.get('PotentialItemName', '')
                fragment['PotentialCategory'] = fragment.get('PotentialCategory', '')
                fragment['ExtractedDescription'] = fragment.get('ExtractedDescription', '')
                fragment['SuggestedLevel'] = fragment.get('SuggestedLevel', '')
                fragment['SuggestedParentName'] = fragment.get('SuggestedParentName', '')
                fragment['ManualReviewFlag'] = fragment.get('ManualReviewFlag', True)
                fragment['ManualReviewNotes'] = fragment.get('ManualReviewNotes', '')
                fragment['人工審核狀態'] = fragment.get('人工審核狀態', '待審核') 
                fragment['人工修正建議'] = fragment.get('人工修正建議', '')
                
            all_knowledge_fragments.extend(current_file_fragments)
    
    logger.info(f"說明書內容提取與細膩拆解完成，共生成 {len(all_knowledge_fragments)} 個知識片段。")
    return pd.DataFrame(all_knowledge_fragments)


def run_manual_extraction_module(df_product_overview=None): 
    """
    執行說明書內容提取與初步結構化模組的主函數。
    """
    logger.info("--- 啟動「說明書內容提取與細膩拆解」模塊 ---")

    if df_product_overview is None:
        logger.warning("警告: 未提供產品總覽數據，將使用模擬數據。")
        df_product_overview = pd.DataFrame({
            'ProductName': ['CPX-900 K1A', 'CPX-900 K1S', 'CPX-900 K2F', 'CPX-900 K2R', 'CPX-900 K2S', 'Super Song 600', 'Super Song V', 'RX-301遙控器'],
            'BOMLink': ['http://bom.com/k1a', '', '', '', '', '', '', ''],
            'ManualLink': ['http://manual.com/k1a', '', '', '', '', '', '', '']
        })
        df_product_overview['ProductName'] = df_product_overview['ProductName'].astype(str)


    manual_extracted_df = process_manual_data(
        MANUALS_DIR,
        EXTRACTED_IMAGES_BASE_DIR,
        EXTRACTED_PDF_PAGE_PREVIEWS_BASE_DIR, 
        df_product_overview
    )

    output_path = os.path.join(TEMP_OUTPUT_DIR, 'temp_manual_extracted_data.xlsx')
    manual_extracted_df.to_excel(output_path, index=False)
    logger.info(f"「說明書細膩拆解數據集」已成功匯出至: {output_path}")
    logger.info("--- 「說明書內容提取與細膩拆解」模塊執行完畢 ---")

    return manual_extracted_df

if __name__ == "__main__":
    run_manual_extraction_module()