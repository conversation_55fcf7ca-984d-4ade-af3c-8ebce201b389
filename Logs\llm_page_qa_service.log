2025-07-24 19:12:37,554 - LLMPageQAService - INFO - --- 啟動 llm_page_qa_service.py 單獨測試 ---
2025-07-24 19:12:37,555 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:12:39,627 - LLMPageQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_page_qa_service.py", line 241, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 19:12:39,660 - LLMPageQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行頁面分析測試。
2025-07-24 19:12:56,911 - LLMPageQAService - INFO - --- 啟動 llm_page_qa_service.py 單獨測試 ---
2025-07-24 19:12:56,911 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:12:59,004 - LLMPageQAService - ERROR - Ollama 連通性測試失敗：HTTP 錯誤 404。請檢查模型名稱是否正確或 Ollama 服務狀態。
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_page_qa_service.py", line 241, in test_ollama_connection
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: http://localhost:11434/api/generate
2025-07-24 19:12:59,008 - LLMPageQAService - CRITICAL - Ollama 服務未運行或無法連接，無法執行頁面分析測試。
2025-07-24 19:13:39,320 - LLMPageQAService - INFO - --- 啟動 llm_page_qa_service.py 單獨測試 ---
2025-07-24 19:13:39,320 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:13:50,451 - LLMPageQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 19:13:50,451 - LLMPageQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 19:14:31,074 - LLMPageQAService - INFO - --- 啟動 llm_page_qa_service.py 單獨測試 ---
2025-07-24 19:14:31,075 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:14:33,972 - LLMPageQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 19:14:33,972 - LLMPageQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 19:21:27,664 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:21:40,303 - LLMPageQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 19:21:40,304 - LLMPageQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 19:21:40,305 - UIPageInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 19:21:43,563 - UIPageInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 19:24:15,695 - LLMPageQAService - INFO - --- 啟動 llm_page_qa_service.py 單獨測試 ---
2025-07-24 19:24:15,696 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:24:18,567 - LLMPageQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 19:24:18,567 - LLMPageQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 19:24:21,619 - LLMPageQAService - INFO - 
--- 測試頁面分析: 產品 'CPX-900 K1A', 頁碼 3 ---
2025-07-24 19:24:41,552 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:25:13,600 - LLMPageQAService - ERROR - Ollama 連通性測試失敗：請求超時。HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=30)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_page_qa_service.py", line 240, in test_ollama_connection
    response = requests.post(OLLAMA_API_URL, headers=headers, json=data, timeout=30)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=30)
2025-07-24 19:25:44,656 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:26:16,729 - LLMPageQAService - ERROR - Ollama 連通性測試失敗：請求超時。HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=30)
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "C:\Python313\Lib\http\client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\http\client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "C:\Python313\Lib\http\client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Python313\Lib\socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
TimeoutError: timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\util\util.py", line 39, in reraise
    raise value
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\urllib3\connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\Scripts\llm_page_qa_service.py", line 240, in test_ollama_connection
    response = requests.post(OLLAMA_API_URL, headers=headers, json=data, timeout=30)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\api.py", line 115, in post
    return request("post", url, data=data, json=json, **kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\api.py", line 59, in request
    return session.request(method=method, url=url, **kwargs)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\Documents\Ark_Knowledge_Engine\venv\Lib\site-packages\requests\adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPConnectionPool(host='localhost', port=11434): Read timed out. (read timeout=30)
2025-07-24 19:26:27,822 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-24 19:26:56,698 - LLMPageQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-24 19:26:56,698 - LLMPageQAService - INFO - 測試回應: 好的，准备好了！
2025-07-24 19:26:56,699 - UIPageInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 19:26:59,716 - UIPageInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 19:27:14,778 - UIPageInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 19:27:16,682 - UIPageInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-24 19:29:17,016 - UIPageInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-24 19:29:18,981 - UIPageInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
2025-07-25 10:04:08,200 - LLMPageQAService - INFO - --- 正在執行 Ollama 連通性測試 ---
2025-07-25 10:04:21,920 - LLMPageQAService - INFO - Ollama 連通性測試成功！模型 'llama3.1' 已響應。
2025-07-25 10:04:21,921 - LLMPageQAService - INFO - 測試回應: 好的，准备好了！
2025-07-25 10:04:21,923 - UIPageInteractiveAITuner - INFO - 正在載入數據: ..\Temp_Processed_Data\temp_manual_extracted_data.xlsx
2025-07-25 10:04:23,445 - UIPageInteractiveAITuner - INFO - 數據載入成功，共 3992 筆記錄。
