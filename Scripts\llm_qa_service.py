# Ark_Knowledge_Engine/Scripts/llm_qa_service.py

import requests
import json
import logging
import re
import os # 新增導入 os 模塊

# --- 配置區 ---
OLLAMA_API_URL = "http://localhost:11434/api/generate"
OLLAMA_MODEL_NAME = "llama3.1" # 使用您確認的模型名稱

# 定義標準化的功能分類選項，從 llm_classify_manual_data.py 複製過來
STANDARD_CATEGORIES = [
    "硬體規格", "軟體功能", "操作流程", "系統設定", "故障排除", "配件說明", "服務信息", "未分類",
    "產品特色說明", "產品功能簡介", "接口說明", "規格說明", "性能參數", "連接方式", "安全須知", 
    "維護與保養", "圖像/多媒體", "表格數據", "原始表格", "版權聲明", "安全提示" 
]

# 日誌配置 (獨立於其他模塊，用於記錄服務本身的交互)
LOG_DIR = os.path.join(os.path.dirname(__file__), '..', 'Logs')
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, 'llm_qa_service.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('LLMQAService')

# --- 輔助函數 ---

def _call_ollama_api(prompt, model_name, api_url):
    """
    調用本地 Ollama API 進行文本生成。
    """
    headers = {"Content-Type": "application/json"}
    data = {
        "model": model_name,
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.1,
            "num_predict": 2048
        }
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=120) 
        response.raise_for_status() 
        result = response.json()
        logger.debug(f"Ollama 原始響應 (部分): {str(result)[:500]}...")
        return result['response']
    except requests.exceptions.ConnectionError as e:
        logger.critical(f"Ollama API 連線失敗: {e}. 請確保 Ollama 服務已啟動並監聽在 {api_url.replace('/api/generate', '')}。", exc_info=True)
        return None
    except requests.exceptions.Timeout as e:
        logger.error(f"Ollama API 請求超時: {e}", exc_info=True)
        return None
    except requests.exceptions.HTTPError as e:
        logger.error(f"Ollama API HTTP 錯誤: {e}. 狀態碼: {response.status_code}. 響應內容: {response.text[:200]}...", exc_info=True)
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Ollama API 返回無效 JSON: {e}. 原始響應: {response.text[:200]}...", exc_info=True)
        return None
    except KeyError:
        logger.error(f"Ollama API 響應格式錯誤，缺少 'response' 鍵。原始響應: {str(result)[:200]}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"調用 Ollama API 時發生未知錯誤: {e}", exc_info=True)
        return None

def _parse_llm_structured_response(response_text, expected_keys):
    """
    解析 LLM 返回的 JSON 字符串，特別針對結構化輸出。
    """
    logger.debug(f"嘗試解析 LLM 響應文本 (部分): {response_text[:500]}...")
    try:
        json_match = re.search(r'```json\s*(\{.*\})\s*```', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            logger.debug(f"從 ```json``` 塊中提取到 JSON 字符串: {json_str[:200]}...")
        else:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}')
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = response_text[json_start : json_end + 1]
                logger.debug(f"直接提取到 JSON 字符串: {json_str[:200]}...")
            else:
                logger.warning(f"未在 LLM 響應中找到有效的 JSON 結構。原始文本 (部分): {response_text[:200]}...")
                return None
        
        parsed_data = json.loads(json_str)
        result = {key: parsed_data.get(key, '') for key in expected_keys}
        return result
    except json.JSONDecodeError as e:
        logger.error(f"解析 LLM 返回的 JSON 失敗: {e}. 原始文本 (部分): {response_text[:500]}...", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"解析 LLM 響應時發生未知錯誤: {e}. 原始文本 (部分): {response_text[:500]}...", exc_info=True)
        return None

# --- 主要服務函數 ---

def get_llm_qa_response(user_question, knowledge_fragment_data, qa_task_type="default_qa"):
    """
    從知識片段數據中提取上下文，構建 Prompt，調用 Ollama 獲取問答響應。
    知識片段數據是一個字典，包含 ExtractedContent, ContentType, PageNumber 等。
    qa_task_type 可以是 "default_qa", "extract_fields", "summarize_content" 等
    """
    # 確保所有傳入的 knowledge_fragment_data 鍵都存在，以避免 KeyError
    extracted_content = knowledge_fragment_data.get('ExtractedContent', '')
    content_type = knowledge_fragment_data.get('ContentType', '未知')
    associated_product_name = knowledge_fragment_data.get('AssociatedProductName', '未知產品')
    page_number = knowledge_fragment_data.get('PageNumber', 'N/A')
    original_font_size = knowledge_fragment_data.get('OriginalFontSize', 'N/A')
    is_bold = knowledge_fragment_data.get('IsBold', False)
    original_bbox = knowledge_fragment_data.get('OriginalBBox', 'N/A')

    # 構建更豐富的上下文提示 (MCP 上下文協議的雛形)
    context_hints = f"這是一個內容類型為 '{content_type}' 的知識片段，位於產品 '{associated_product_name}' 的第 {page_number} 頁。"
    if content_type.startswith('header'):
        context_hints += f"它是一個標題，字體大小約 {original_font_size}，且{'加粗' if is_bold else '未加粗'}。"
    elif content_type == 'table':
        context_hints += "這是一個表格，內容已轉換為 Markdown 格式。"
    elif content_type == 'image':
        context_hints += "這是一個圖片文件的描述。你可能需要參考其文件名或推斷其圖說。"
    elif content_type == 'list_item':
        context_hints += "這是一個列表中的項目。"
    
    context_hints += f" 頁面信息: {page_number}。" # 將頁碼信息也加入提示

    system_instruction = ""
    json_output_format = ""
    expected_output_keys = []

    if qa_task_type == "extract_fields":
        system_instruction = f"""
        你是一個精確的產品數據提取助理，請你根據用戶提供的知識片段內容，提取以下關鍵信息：
        - item_name (項目名稱): 精簡具體的功能點或規格點。
        - category (分類): 從標準分類 {', '.join(STANDARD_CATEGORIES)} 中選擇最合適的。
        - description (精煉描述): 簡潔準確的總結，約30-100字。
        - level (建議層級): 判斷在產品功能結構中的層級 (1-4)。
        - parent_name (建議父級名稱): 若有上級功能。
        請只以 JSON 格式輸出，不要包含任何額外文字解釋。
        """
        json_output_format = """
        ```json
        {
            "item_name": "範例項目",
            "category": "範例分類",
            "description": "範例描述",
            "level": 0,
            "parent_name": "None"
        }
        ```
        """
        expected_output_keys = ['item_name', 'category', 'description', 'level', 'parent_name']
        user_question_for_llm = "請根據以下知識片段和上下文，提取上述關鍵信息。"
    elif qa_task_type == "default_qa":
        system_instruction = "你是一個樂於助人的產品知識問答助理，請你根據提供的知識片段及其上下文，簡潔地回答用戶的問題。"
        user_question_for_llm = user_question # 用戶直接提問
        json_output_format = "" # 不要求JSON格式，直接文本回答
        expected_output_keys = [] # 不解析JSON
    elif qa_task_type == "summarize_content":
        system_instruction = "你是一個內容總結者，請你將以下知識片段的內容精煉為一個簡潔的總結，約50-100字。"
        user_question_for_llm = "請總結以下知識片段的核心內容。"
        json_output_format = "" # 不要求JSON格式，直接文本回答
        expected_output_keys = [] # 不解析JSON
    else:
        logger.error(f"不支持的問答任務類型: {qa_task_type}")
        return None, "不支持的問答任務類型。"

    prompt = f"""
    {system_instruction}

    ---
    產品名稱: {associated_product_name}
    內容上下文提示: {context_hints}
    知識片段內容:
    {extracted_content}
    ---

    用戶問題: {user_question_for_llm}
    {json_output_format}
    """
    
    logger.debug(f"生成的 Prompt (部分): {prompt[:500]}...")
    
    llm_response_text = _call_ollama_api(prompt, OLLAMA_MODEL_NAME, OLLAMA_API_URL)

    if llm_response_text:
        if qa_task_type == "extract_fields":
            parsed_result = _parse_llm_structured_response(llm_response_text, expected_output_keys)
            return parsed_result, None # 返回解析後的字典
        else:
            return llm_response_text, None # 返回原始文本
    else:
        return None, "LLM 未能生成有效響應。"

# --- 測試 Ollama 連通性 (用於驗證服務是否可用) ---
def test_ollama_connection():
    """
    測試 Ollama 服務的基本連通性。
    """
    logger.info("--- 正在執行 Ollama 連通性測試 ---")
    test_prompt = "你好，你準備好了嗎？請簡短回答。"
    headers = {"Content-Type": "application/json"}
    data = {
        "model": OLLAMA_MODEL_NAME, 
        "prompt": test_prompt,
        "stream": False,
        "options": {"temperature": 0.1, "num_predict": 50}
    }

    try:
        response = requests.post(OLLAMA_API_URL, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        if "response" in result and result["response"].strip():
            logger.info(f"Ollama 連通性測試成功！模型 '{OLLAMA_MODEL_NAME}' 已響應。")
            logger.info(f"測試回應: {result['response'].strip()}")
            return True
        else:
            logger.error(f"Ollama 連通性測試失敗：未收到有效回應。原始響應: {result}")
            return False
    except requests.exceptions.ConnectionError as e:
        logger.critical(f"Ollama 連通性測試失敗：無法連接到服務器。請確保 Ollama 服務已啟動並監聽在 {OLLAMA_API_URL.replace('/api/generate', '')}。", exc_info=True)
        return False
    except requests.exceptions.Timeout as e:
        logger.error(f"Ollama 連通性測試失敗：請求超時。{e}", exc_info=True)
        return False
    except requests.exceptions.HTTPError as e:
        logger.error(f"Ollama 連通性測試失敗：HTTP 錯誤 {e.response.status_code}。請檢查模型名稱是否正確或 Ollama 服務狀態。", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Ollama 連通性測試時發生未知錯誤：{e}", exc_info=True)
        return False


if __name__ == "__main__":
    if test_ollama_connection():
        logger.info("Ollama 服務準備就緒。")
        sample_fragment = {
            'ExtractedContent': "本產品採用高清晰度多媒體介面(HDMI™)技術,使用此介面能傳輸最佳品質的數位影像訊號。",
            'ContentType': 'paragraph',
            'AssociatedProductName': 'CPX-900 K1A',
            'PageNumber': 5,
            'OriginalFontSize': 12,
            'IsBold': False,
            'OriginalBBox': '(100, 200, 500, 250)'
        }
        
        logger.info("\n--- 測試默認問答 ---")
        response, error = get_llm_qa_response("這段文字講了什麼？", sample_fragment, qa_task_type="default_qa")
        if response:
            logger.info(f"LLM 回答: {response}")
        else:
            logger.error(f"LLM 回答失敗: {error}")
            
        logger.info("\n--- 測試字段提取 ---")
        extracted_fields, error = get_llm_qa_response("提取此片段的功能名稱、分類、描述、層級和父級名稱。", sample_fragment, qa_task_type="extract_fields")
        if extracted_fields:
            logger.info(f"提取結果: {extracted_fields}")
        else:
            logger.error(f"字段提取失敗: {error}")
    else:
        logger.critical("Ollama 服務未運行或無法連接，無法執行問答測試。")