# Ark_Knowledge_Engine/Scripts/llm_page_qa_service.py

import requests
import json
import logging
import re
import os
import pandas as pd
from collections import defaultdict # 用於聚合頁面內容

# --- 配置區 ---
OLLAMA_API_URL = "http://localhost:11434/api/generate"
OLLAMA_MODEL_NAME = "llama3.1" # 使用您確認的模型名稱

STANDARD_CATEGORIES = [
    "硬體規格", "軟體功能", "操作流程", "系統設定", "故障排除", "配件說明", "服務信息", "未分類",
    "產品特色說明", "產品功能簡介", "接口說明", "規格說明", "性能參數", "連接方式", "安全須知", 
    "維護與保養", "圖像/多媒體", "表格數據", "原始表格", "版權聲明", "安全提示" 
]

# 日誌配置
LOG_DIR = os.path.join(os.path.dirname(__file__), '..', 'Logs')
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, 'llm_page_qa_service.log') # 新的日誌文件

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('LLMPageQAService')

# --- 輔助函數 ---

def _call_ollama_api(prompt, model_name, api_url):
    """
    調用本地 Ollama API 進行文本生成。
    """
    headers = {"Content-Type": "application/json"}
    data = {
        "model": model_name,
        "prompt": prompt,
        "stream": False, 
        "options": {
            "temperature": 0.1, 
            "num_predict": 4096 # 對於整頁內容，允許更長的響應
        }
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=300) # 更長的超時時間
        response.raise_for_status() 
        result = response.json()
        logger.debug(f"Ollama 原始響應 (部分): {str(result)[:500]}...")
        return result['response']
    except requests.exceptions.ConnectionError as e:
        logger.critical(f"Ollama API 連線失敗: {e}. 請確保 Ollama 服務已啟動並監聽在 {api_url.replace('/api/generate', '')}。", exc_info=True)
        return None
    except requests.exceptions.Timeout as e:
        logger.error(f"Ollama API 請求超時: {e}", exc_info=True)
        return None
    except requests.exceptions.HTTPError as e:
        logger.error(f"Ollama API HTTP 錯誤: {e}. 狀態碼: {response.status_code}. 響應內容: {response.text[:200]}...", exc_info=True)
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Ollama API 返回無效 JSON: {e}. 原始響應: {response.text[:200]}...", exc_info=True)
        return None
    except KeyError:
        logger.error(f"Ollama API 響應格式錯誤，缺少 'response' 鍵。原始響應: {str(result)[:200]}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"調用 Ollama API 時發生未知錯誤: {e}", exc_info=True)
        return None

def _parse_llm_page_structured_response(response_text):
    """
    解析 LLM 返回的針對整頁內容的 JSON 字符串。
    預期返回一個包含多個知識片段或總結的 JSON 列表。
    """
    logger.debug(f"嘗試解析 LLM 整頁響應文本 (部分): {response_text[:500]}...")
    try:
        json_match = re.search(r'```json\s*(\[.*\])\s*```', response_text, re.DOTALL) # 尋找 JSON 數組
        if json_match:
            json_str = json_match.group(1)
            logger.debug(f"從 ```json``` 塊中提取到 JSON 字符串: {json_str[:200]}...")
        else:
            # 如果沒有 ```json``` 包裹，嘗試直接解析
            json_start = response_text.find('[')
            json_end = response_text.rfind(']')
            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = response_text[json_start : json_end + 1]
                logger.debug(f"直接提取到 JSON 數組字符串: {json_str[:200]}...")
            else:
                logger.warning(f"未在 LLM 響應中找到有效的 JSON 數組結構或 ```json``` 包裹。原始文本: {response_text[:200]}...")
                return None
        
        parsed_data = json.loads(json_str)
        if not isinstance(parsed_data, list): # 確保是列表
            logger.error(f"LLM 響應 JSON 不是預期的列表格式。原始響應: {response_text[:200]}...")
            return None
        
        # 驗證每個字典的結構
        validated_results = []
        for item in parsed_data:
            if isinstance(item, dict):
                validated_results.append({
                    'item_name': item.get('item_name', ''),
                    'category': item.get('category', ''),
                    'description': item.get('description', ''),
                    'level': item.get('level', ''),
                    'parent_name': item.get('parent_name', '')
                })
        return validated_results # 返回解析後的列表
    except json.JSONDecodeError as e:
        logger.error(f"解析 LLM 返回的 JSON 失敗: {e}. 原始文本: {response_text[:500]}...", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"解析 LLM 響應時發生未知錯誤: {e}. 原始文本: {response_text[:500]}...", exc_info=True)
        return None


def get_llm_page_analysis(page_fragments, associated_product_name, page_number):
    """
    接收一頁的所有知識片段，構建 Prompt，調用 Ollama 進行整頁分析。
    page_fragments 是列表，每個元素是一個知識片段字典 (包含 ContentType, ExtractedContent 等)。
    """
    if not page_fragments:
        return None, "無知識片段可供分析。"
    
    # 組合整頁的上下文
    combined_page_content = []
    for fragment in page_fragments:
        content_type = fragment.get('ContentType', '未知')
        extracted_content = fragment.get('ExtractedContent', '')
        original_id = fragment.get('OriginalID', 'N/A')
        
        # 根據 ContentType 格式化，提供 LLM 更多結構線索
        if content_type.startswith('header'):
            combined_page_content.append(f"## {extracted_content} (類型: {content_type}, ID: {original_id})")
        elif content_type == 'table':
            combined_page_content.append(f"\n### 表格內容 (類型: {content_type}, ID: {original_id})\n{extracted_content}\n")
        elif content_type == 'image':
            combined_page_content.append(f"\n### 圖片描述 (類型: {content_type}, ID: {original_id})\n{extracted_content}\n")
        else: # paragraph, list_item, plain_text, raw_table_text
            combined_page_content.append(f"{extracted_content} (類型: {content_type}, ID: {original_id})")
            
    # LLM Prompt
    system_instruction = f"""
    你是一個經驗豐富的產品文檔分析師，請你仔細閱讀以下產品說明書的**單頁內容**。
    你的任務是從這整頁的知識片段中，識別並提取出該頁的**主要功能項目、規格、操作、圖示或任何關鍵資訊點**。請你像構建思維導圖一樣，為每個關鍵資訊點提供其「項目名稱」、「分類」、「精煉描述」、「建議層級」和「建議父級名稱」。

    請遵循以下嚴格要求：
    1.  **輸出格式**：請**只**以一個 JSON 數組（列表）的形式返回結果。數組中的每個對象代表一個被你識別出的關鍵資訊點。不要包含任何額外文字解釋。
    2.  **每個對象的鍵**：必須包含以下鍵：`item_name`、`category`、`description`、`level`、`parent_name`。
    3.  **item_name (項目名稱)**：應簡潔、具體，能代表一個功能、規格、介面、操作步驟等。使用繁體中文。例如：「開機步驟」、「HDMI 接口」、「記憶體容量」。
    4.  **category (分類)**：從以下標準分類中選擇最合適的一個：{', '.join(STANDARD_CATEGORIES)}。
    5.  **description (精煉描述)**：針對該 `item_name` 的簡潔總結，字數約 30-100 字。
    6.  **level (建議層級)**：判斷此項目在頁面或產品功能結構中的大概層級。
        * 1：頁面主題或最頂層概述。
        * 2：主要功能模塊或大項。
        * 3：具體功能點或規格細節。
        * 4或更高：更細的參數或操作步驟。
        請根據頁面標題、字體大小、內容類型和其在頁面中的位置來推斷層級。
    7.  **parent_name (建議父級名稱)**：如果此項目有明確的上級功能或模塊，請提供其「項目名稱」。如果為該頁的頂層項目，請填寫 "None"。
    8.  **引用來源（重要）**：如果可以，請在 `description` 中引用你提取信息的原始片段的 `OriginalID` 和 `PageNumber`，例如：`根據內容，此功能支援藍牙5.0 [引用來源：CPX-900_P5_T_12, 頁碼 5]。`

    ---
    產品名稱: {associated_product_name}
    頁碼: {page_number}
    本頁所有知識片段內容:
    {'\n'.join(combined_page_content)}
    ---

    你的 JSON 輸出範例 (數組形式):
    ```json
    [
        {{
            "item_name": "產品功能簡介",
            "category": "產品功能簡介",
            "description": "本頁概述了產品的多項核心功能，例如點唱機、SD卡支援等 [引用來源：CPX-900_P5_T_1]。" ,
            "level": 1,
            "parent_name": "None"
        }},
        {{
            "item_name": "點唱機功能",
            "category": "軟體功能",
            "description": "本產品是高科技多媒體電腦伴唱機，可自錄伴唱歌曲影片，重複點歌歡唱 [引用來源：CPX-900_P5_T_2]。",
            "level": 2,
            "parent_name": "產品功能簡介"
        }},
        {{
            "item_name": "HDMI 傳輸技術",
            "category": "接口說明",
            "description": "本型號採用高清晰度多媒體介面(HDMI™)技術，能傳輸最佳品質的數位影像訊號 [引用來源：CPX-900_P5_T_5]。",
            "level": 2,
            "parent_name": "產品功能簡介"
        }},
        {{
            "item_name": "SD卡儲存",
            "category": "硬體規格",
            "description": "SD卡用以儲存MIDI歌曲，請勿拿至電腦讀寫，避免造成SD卡內的資料毀損 [引用來源：CPX-900_P5_T_3]。",
            "level": 3,
            "parent_name": "點唱機功能"
        }}
    ]
    ```
    現在請解析以上頁面內容，並輸出 JSON：
    """

    prompt = system_instruction
    
    llm_response_text = _call_ollama_api(prompt, OLLAMA_MODEL_NAME, OLLAMA_API_URL)

    if llm_response_text:
        # 這裡的 expected_keys 不再是一個固定列表，而是由 _parse_llm_page_structured_response 決定其結構
        parsed_result = _parse_llm_page_structured_response(llm_response_text)
        return parsed_result, None # 返回解析後的列表
    else:
        return None, "LLM 未能生成有效響應。"

# --- 測試 Ollama 連通性 (沿用之前的邏輯) ---
def test_ollama_connection():
    """
    測試 Ollama 服務的基本連通性。
    """
    logger.info("--- 正在執行 Ollama 連通性測試 ---")
    test_prompt = "你好，你準備好了嗎？請簡短回答。"
    headers = {"Content-Type": "application/json"}
    data = {
        "model": OLLAMA_MODEL_NAME, 
        "prompt": test_prompt,
        "stream": False,
        "options": {"temperature": 0.1, "num_predict": 50}
    }

    try:
        response = requests.post(OLLAMA_API_URL, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        if "response" in result and result["response"].strip():
            logger.info(f"Ollama 連通性測試成功！模型 '{OLLAMA_MODEL_NAME}' 已響應。")
            logger.info(f"測試回應: {result['response'].strip()}")
            return True
        else:
            logger.error(f"Ollama 連通性測試失敗：未收到有效回應。原始響應: {result}")
            return False
    except requests.exceptions.ConnectionError as e:
        logger.critical(f"Ollama 連通性測試失敗：無法連接到服務器。請確保 Ollama 服務已啟動並監聽在 {OLLAMA_API_URL.replace('/api/generate', '')}。", exc_info=True)
        return False
    except requests.exceptions.Timeout as e:
        logger.error(f"Ollama 連通性測試失敗：請求超時。{e}", exc_info=True)
        return False
    except requests.exceptions.HTTPError as e:
        logger.error(f"Ollama 連通性測試失敗：HTTP 錯誤 {e.response.status_code}。請檢查模型名稱是否正確或 Ollama 服務狀態。", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Ollama 連通性測試時發生未知錯誤：{e}", exc_info=True)
        return False


if __name__ == "__main__":
    # 這個 main 塊僅用於單獨測試 llm_page_qa_service.py 的連通性和頁面分析功能
    logger.info("--- 啟動 llm_page_qa_service.py 單獨測試 ---")
    if test_ollama_connection():
        # 載入一個真實的 temp_manual_extracted_data.xlsx 數據進行測試
        # 假設該文件已由 extract_manual_data.py 生成
        temp_data_path = os.path.join(os.path.dirname(__file__), '..', 'Temp_Processed_Data', 'temp_manual_extracted_data.xlsx')
        if os.path.exists(temp_data_path):
            df_all_fragments = pd.read_excel(temp_data_path)
            
            # 選擇一個產品和頁碼進行測試，例如 'CPX-900 K1A' 的第 3 頁 (目錄頁)
            test_product_name = 'CPX-900 K1A'
            test_page_num = 3 # 替換為包含目錄或多個功能點的頁碼
            
            test_page_fragments = df_all_fragments[
                (df_all_fragments['AssociatedProductName'] == test_product_name) &
                (df_all_fragments['PageNumber'] == test_page_num)
            ].to_dict(orient='records') # 將篩選出的片段轉為字典列表

            if test_page_fragments:
                logger.info(f"\n--- 測試頁面分析: 產品 '{test_product_name}', 頁碼 {test_page_num} ---")
                
                # 調用新的整頁分析函數
                analysis_results, error = get_llm_page_analysis(test_page_fragments, test_product_name, test_page_num)
                
                if analysis_results:
                    logger.info("LLM 頁面分析成功！結果如下:")
                    for item in analysis_results:
                        logger.info(f"  - 項目: {item.get('item_name', 'N/A')}, 分類: {item.get('category', 'N/A')}, 層級: {item.get('level', 'N/A')}, 父級: {item.get('parent_name', 'N/A')}")
                        logger.info(f"    描述: {item.get('description', 'N/A')[:100]}...") # 打印部分描述
                else:
                    logger.error(f"LLM 頁面分析失敗: {error}")
            else:
                logger.warning(f"未找到產品 '{test_product_name}' 頁碼 {test_page_num} 的知識片段，無法執行頁面分析測試。")
        else:
            logger.warning(f"請先運行 extract_manual_data.py 以生成 {temp_data_path} 文件。")
    else:
        logger.critical("Ollama 服務未運行或無法連接，無法執行頁面分析測試。")