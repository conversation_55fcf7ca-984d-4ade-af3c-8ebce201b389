# Ark_Knowledge_Engine/Scripts/ui_page_interactive_ai_tuner.py

import streamlit as st
import pandas as pd
import os
import logging
import json
from datetime import datetime

# 導入新的頁次導向服務
import llm_page_qa_service 

# --- 配置區 ---
DATA_SOURCE_PATH = os.path.join('..', 'Temp_Processed_Data', 'temp_manual_extracted_data.xlsx') 
DATA_OUTPUT_PATH = DATA_SOURCE_PATH 

EXTRACTED_IMAGES_BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'Temp_Processed_Data', 'Extracted_Images'))
PDF_PAGE_PREVIEWS_BASE_DIR = os.path.join(EXTRACTED_IMAGES_BASE_DIR, "Page_Previews")

MANUALS_DIR_FOR_UI_LINK = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'Raw_Data_Sources', 'Product_Manuals'))

LOG_DIR = os.path.join('..', 'Logs')
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, 'ui_page_interactive_ai_tuner.log') 

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UIPageInteractiveAITuner')

CATEGORY_OPTIONS = [
    "硬體規格", "軟體功能", "操作流程", "系統設定", "故障排除", "配件說明", "服務信息", "未分類",
    "產品特色說明", "產品功能簡介", "接口說明", "規格說明", "性能參數", "連接方式", "安全須知", "維護與保養", "圖像/多媒體", "表格數據", "原始表格", "版權聲明", "安全提示"
]
REVIEW_STATUS_OPTIONS = ["待審核", "已確認", "需修正"]

# --- 定義期望的欄位作為全局常量 ---
EXPECTED_COLS_FOR_UI = [
    'SourceType', 'SourceFile', 'AssociatedProductName', 'OriginalID', 
    'ExtractedRawText', 'ExtractedImagePaths', 'PdfPageImagePaths', 
    'ExtractedContent', 
    'ContentType', 
    'PageNumber', 
    'OriginalFontSize', 'IsBold', 'OriginalBBox', 
    'ExtractedDescription', 
    'PotentialItemName', 'PotentialCategory', 'SuggestedLevel', 'SuggestedParentName', 
    'ManualReviewFlag', 'ManualReviewNotes', 'BOMLink', 'ManualLink', 'LastProcessedDate',
    'BOMID', 'PartNumberID_BOM', 'PartName_PartLib', 'Specification_PartLib', 
    'Location_BOM', 'PartCategory_PartLib', 'Quantity_BOM', 'Unit_BOM',
    'BOMType_BOM', 'Version_BOM', 'EffectiveDate_BOM', 'OriginalNotes',
    '問題追蹤ID', '問題狀態', '問題描述_問題追蹤', 
    '人工審核狀態', '人工修正建議'
]

# --- 數據載入與保存函數 ---

def load_data(file_path):
    """載入數據。"""
    if os.path.exists(file_path):
        logger.info(f"正在載入數據: {file_path}")
        df = pd.read_excel(file_path)
        
        for col in EXPECTED_COLS_FOR_UI: 
            if col not in df.columns:
                df[col] = '' 
            else:
                # 確保填充 NaN 後再轉換類型
                df[col] = df[col].fillna('') 

        # 強制轉換特定欄位類型，避免AttributeError
        for col in ['ExtractedImagePaths', 'PdfPageImagePaths']:
            if col in df.columns:
                df[col] = df[col].astype(str) # 確保是字符串
        
        # 數字類型處理
        for col in ['PageNumber', 'OriginalFontSize']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
        
        # 布林值處理
        if 'IsBold' in df.columns:
            df['IsBold'] = df['IsBold'].apply(lambda x: str(x).lower() == 'true')

        # 其餘列轉為字符串
        # 排除數字和布林類型，以及已處理的圖片路徑欄位
        cols_to_str_safe = [col for col in EXPECTED_COLS_FOR_UI if col not in ['PageNumber', 'OriginalFontSize', 'IsBold', 'ExtractedImagePaths', 'PdfPageImagePaths']]
        for col in cols_to_str_safe:
            if col in df.columns: 
                df[col] = df[col].astype(str)
        
        logger.info(f"數據載入成功，共 {len(df)} 筆記錄。")
        return df
    else:
        logger.error(f"錯誤: 數據檔案不存在: {file_path}。請先運行 extract_manual_data.py。")
        st.error(f"錯誤: 數據檔案不存在。請先運行 `extract_manual_data.py` 生成 `{os.path.basename(file_path)}`。")
        return pd.DataFrame(columns=EXPECTED_COLS_FOR_UI)

def save_data(df, file_path):
    """保存數據到 Excel 檔案。"""
    try:
        df.to_excel(file_path, index=False)
        logger.info(f"數據已成功保存至: {file_path}")
        st.success("數據保存成功！")
    except Exception as e:
        logger.error(f"錯誤: 保存數據失敗到 '{file_path}': {e}", exc_info=True)
        st.error(f"保存數據失敗: {e}")

# --- Streamlit UI 介面 ---

def main():
    st.set_page_config(layout="wide", page_title="方舟知識引擎 - 頁次調教平台")
    
    st.title("📄 方舟知識引擎 - 頁次調教平台")
    st.sidebar.title("導航")

    # 檢查 Ollama 連通性
    if 'ollama_connected' not in st.session_state:
        st.session_state.ollama_connected = llm_page_qa_service.test_ollama_connection()
        if not st.session_state.ollama_connected:
            st.error("Ollama 服務未運行或無法連接。請啟動 Ollama 服務並確保模型已加載。")
            st.stop() 

    # 載入數據 (現在是所有細膩拆解的知識片段)
    df_all_fragments = load_data(DATA_SOURCE_PATH)

    if df_all_fragments.empty:
        st.stop()

    # --- 頁面導航邏輯 ---
    # 獲取所有獨特的產品和頁碼組合
    unique_product_pages = df_all_fragments[['AssociatedProductName', 'PageNumber']].drop_duplicates().sort_values(by=['AssociatedProductName', 'PageNumber']).reset_index(drop=True)
    
    # 將每個產品-頁碼組合轉換為一個可選字符串
    unique_product_page_options = [
        f"{row['AssociatedProductName']} - 頁碼 {row['PageNumber']}" 
        for idx, row in unique_product_pages.iterrows()
    ]
    
    if not unique_product_page_options:
        st.warning("沒有可供調教的產品頁面。")
        st.stop()

    if 'current_page_idx' not in st.session_state:
        st.session_state.current_page_idx = 0
    if 'df_fragments' not in st.session_state: 
        st.session_state.df_fragments = df_all_fragments.copy()
    if 'page_analysis_results' not in st.session_state: 
        st.session_state.page_analysis_results = {} 
    if 'manual_page_data' not in st.session_state: 
        st.session_state.manual_page_data = {} 

    # 確保 current_page_idx 在有效範圍內
    if st.session_state.current_page_idx >= len(unique_product_page_options):
        st.session_state.current_page_idx = 0

    # 選擇當前產品和頁碼
    selected_page_option = st.sidebar.selectbox("選擇產品與頁碼", unique_product_page_options, index=st.session_state.current_page_idx, key='page_selector')
    
    # 解析選中的產品名和頁碼
    selected_product_name = selected_page_option.split(' - ')[0]
    selected_page_num = int(selected_page_option.split('頁碼 ')[1])

    # 過濾出當前頁面的所有知識片段
    current_page_fragments_df = st.session_state.df_fragments[
        (st.session_state.df_fragments['AssociatedProductName'] == selected_product_name) &
        (st.session_state.df_fragments['PageNumber'] == selected_page_num)
    ].copy().reset_index(drop=True) 

    if current_page_fragments_df.empty:
        st.warning(f"找不到產品 '{selected_product_name}' 頁碼 {selected_page_num} 的知識片段。")
        st.stop()
    
    # --- 頁面導航按鈕 ---
    total_unique_pages = len(unique_product_page_options)
    # 修正錯誤：直接使用 st.session_state.current_page_idx
    st.markdown(f"<p style='text-align: center;'>**當前頁面 {st.session_state.current_page_idx + 1} / {total_unique_pages}**</p>", unsafe_allow_html=True)
    
    col1_page_nav, col2_page_nav = st.sidebar.columns(2)
    with col1_page_nav:
        if st.button("上一頁", use_container_width=True, key='prev_page_btn'):
            if st.session_state.current_page_idx > 0:
                st.session_state.current_page_idx -= 1
                st.rerun()
            else:
                st.info("這已是第一頁。")
    with col2_page_nav:
        if st.button("下一頁", use_container_width=True, key='next_page_btn'):
            if st.session_state.current_page_idx < total_unique_pages - 1:
                st.session_state.current_page_idx += 1
                st.rerun()
            else:
                st.info("這已是最後一頁。")
    
    # 刪除按鈕 (整頁刪除)
    st.sidebar.markdown("---")
    if st.sidebar.button("🗑️ 刪除當前頁面所有記錄", use_container_width=True, help="此操作不可復原，將刪除此頁面所有知識片段"):
        confirm_placeholder = st.sidebar.empty() # 使用 placeholder 控制顯示
        with confirm_placeholder:
            st.warning(f"確認刪除產品 '{selected_product_name}' 頁碼 {selected_page_num} 的所有記錄？此操作不可復原！", icon="⚠️")
            col_confirm_page_btns = st.sidebar.columns(2)
            with col_confirm_page_btns[0]:
                if st.button("確認刪除此頁", key="confirm_delete_page_btn"):
                    st.session_state.df_fragments = st.session_state.df_fragments[
                        ~((st.session_state.df_fragments['AssociatedProductName'] == selected_product_name) &
                          (st.session_state.df_fragments['PageNumber'] == selected_page_num))
                    ].reset_index(drop=True)
                    save_data(st.session_state.df_fragments, DATA_OUTPUT_PATH)
                    st.success(f"頁面 '{selected_product_name}' - 頁碼 {selected_page_num} 的記錄已刪除並保存。")
                    st.session_state.current_page_idx = max(0, st.session_state.current_page_idx - 1) 
                    st.rerun()
            with col_confirm_page_btns[1]:
                if st.button("取消", key="cancel_delete_page_btn"):
                    st.info("刪除操作已取消。")
                    st.rerun() 
    
    st.sidebar.markdown("---")
    st.sidebar.subheader("最近日誌")
    try:
        with open(LOG_FILE, 'r', encoding='utf-8') as f:
            log_content = f.read().splitlines()
            st.sidebar.text_area("日誌輸出", "\n".join(log_content[-15:]), height=200, disabled=True)
    except FileNotFoundError:
        st.sidebar.info("日誌檔案尚未生成。")


    # --- 主內容區 ---
    st.header(f"產品: {selected_product_name} - 頁碼 {selected_page_num}")
    st.markdown(f"**來源文件:** `{current_page_fragments_df['SourceFile'].iloc[0] if not current_page_fragments_df.empty else 'N/A'}`")
    
    main_col_left, main_col_right = st.columns([0.5, 0.5])

    with main_col_left:
        st.markdown("### 頁面原始數據預覽")
        
        # --- PDF 頁面預覽 ---
        # 獲取該頁的預覽圖路徑 (從第一個片段獲取即可，因為都一樣)
        pdf_page_image_paths_str = current_page_fragments_df['PdfPageImagePaths'].iloc[0]
        # 確保是字符串且不為 'nan'
        if isinstance(pdf_page_image_paths_str, str) and pdf_page_image_paths_str and pdf_page_image_paths_str.lower() != 'nan': 
            pdf_page_paths = pdf_page_image_paths_str.split(";")
            target_page_preview_path = None
            current_page_num_for_display = current_page_fragments_df['PageNumber'].iloc[0] # 獲取當前頁碼

            for p_path in pdf_page_paths:
                if f"_Page{current_page_num_for_display}.png" in p_path: # 用於匹配當前頁
                    target_page_preview_path = p_path
                    break
            
            if target_page_preview_path:
                full_preview_path = os.path.join(EXTRACTED_IMAGES_BASE_DIR, target_page_preview_path)
                if os.path.exists(full_preview_path):
                    st.image(full_preview_path, caption=f"頁面預覽 ({os.path.basename(target_page_preview_path)})", use_column_width=True)
                else:
                    st.warning(f"PDF 頁面預覽圖片檔案不存在或路徑錯誤: {full_preview_path}")
            elif pdf_page_paths: 
                full_preview_path = os.path.join(EXTRACTED_IMAGES_BASE_DIR, pdf_page_paths[0])
                if os.path.exists(full_preview_path):
                    st.image(full_preview_path, caption=f"頁面預覽 (第一頁) ({os.path.basename(pdf_page_paths[0])})", use_column_width=True)
                else:
                    st.info("無 PDF 頁面預覽圖路徑。")
        else:
            st.info("非 PDF 文件或無 PDF 頁面預覽圖。")
        
        # 原始檔案連結
        source_file_name = current_page_fragments_df['SourceFile'].iloc[0] if not current_page_fragments_df.empty else ''
        source_file_path = os.path.join(MANUALS_DIR_FOR_UI_LINK, source_file_name) 
        if os.path.exists(source_file_path):
            display_path = source_file_path.replace(os.sep, '/')
            st.markdown(f"**原始檔案連結:** [{source_file_name}](file:///{display_path})")
        else:
            st.warning(f"原始檔案 '{source_file_name}' 不存在於 `{MANUALS_DIR_FOR_UI_LINK}`。")

        st.markdown("---")
        st.markdown("### 頁面知識片段列表")
        # 顯示頁面內部的所有知識片段，可展開查看詳細
        for idx, fragment_row in current_page_fragments_df.iterrows():
            fragment_type = fragment_row['ContentType']
            fragment_id_display = fragment_row['OriginalID'].split('_')[-1] # 只顯示最後一部分ID
            
            with st.expander(f"片段 {idx\+1} ({fragment_type.upper()}) - ID: {fragment_id_display}", expanded=False):
                st.markdown(f"**內容:**\n```\n{fragment_row['ExtractedContent']}\n```")
                st.markdown(f"**原始ID:** `{fragment_row['OriginalID']}`")
                st.markdown(f"**原始字體大小:** `{fragment_row['OriginalFontSize']}` | **是否加粗:** `{fragment_row['IsBold']}`")
                st.markdown(f"**原始邊界框:** `{fragment_row['OriginalBBox']}`")
                
                # 顯示相關圖片
                image_paths_str = fragment_row['RelatedImagePaths']
                if isinstance(image_paths_str, str) and image_paths_str and image_paths_str.lower() != 'nan':
                    images = image_paths_str.split(";")
                    for img_rel_path in images:
                        full_img_path = os.path.join(EXTRACTED_IMAGES_BASE_DIR, img_rel_path)
                        if os.path.exists(full_img_path):
                            st.image(full_img_path, caption=os.path.basename(img_rel_path), width=200)
                        else:
                            st.warning(f"內嵌圖片檔案不存在: {full_img_path}")


    with main_col_right:
        st.markdown("### AI 頁面分析結果與人工調教")

        # --- AI 頁面分析按鈕 ---
        if st.button("💡 讓 AI 分析本頁內容", use_container_width=True, key='analyze_page_btn'):
            with st.spinner("AI 正在分析整頁內容..."):
                analysis_results, error = llm_page_qa_service.get_llm_page_analysis(
                    current_page_fragments_df.to_dict(orient='records'),
                    selected_product_name,
                    selected_page_num
                )
                if analysis_results:
                    st.session_state.page_analysis_results[(selected_product_name, selected_page_num)] = analysis_results
                    st.success("AI 頁面分析完成！")
                else:
                    st.error(f"AI 頁面分析失敗: {error}")
            st.rerun() 

        # --- 顯示 AI 頁面分析結果 ---
        page_key = (selected_product_name, selected_page_num)
        # 從 session_state 獲取 AI 分析結果，如果沒有則嘗試從當前頁面數據中獲取之前保存的
        ai_page_analysis_data = st.session_state.page_analysis_results.get(page_key)
        
        # 如果 session_state 沒有，嘗試從 df_fragments 中獲取之前保存的結果
        if not ai_page_analysis_data and not current_page_fragments_df.empty:
            # 假設頁面級的分析結果會保存在該頁的第一條記錄的某個字段中
            first_record_page_analysis_str = current_page_fragments_df['PageLLMAnalysisResults'].iloc[0]
            if first_record_page_analysis_str and first_record_page_analysis_str != 'nan':
                try:
                    ai_page_analysis_data = json.loads(first_record_page_analysis_str)
                    st.session_state.page_analysis_results[page_key] = ai_page_analysis_data # 載入到 session_state
                except json.JSONDecodeError:
                    logger.warning(f"頁面 {page_key} 之前保存的 PageLLMAnalysisResults 無效 JSON。")
        

        if ai_page_analysis_data:
            st.markdown("---")
            st.markdown("**AI 頁面分析結果 (可點擊應用到編輯區):**")
            
            if isinstance(ai_page_analysis_data, list): # 確保是列表
                for i, item in enumerate(ai_page_analysis_data):
                    with st.expander(f"AI 建議項目 {i+1}: {item.get('item_name', 'N/A')}", expanded=True):
                        st.json(item)
                        if st.button(f"✅ 應用此 AI 建議到頁面調教欄位 ({i+1})", key=f"apply_ai_item_{current_df_actual_index}_{i}"):
                            # 將 AI 建議填充到頁面級人工調教數據 (保存到 session_state.manual_page_data)
                            st.session_state.manual_page_data[page_key] = {
                                'item_name': item.get('item_name', ''),
                                'category': item.get('category', ''),
                                'description': item.get('description', ''),
                                'level': item.get('level', ''),
                                'parent_name': item.get('parent_name', ''),
                                'review_status': '待審核', 
                                'notes': ''
                            }
                            st.success(f"AI 建議項目 '{item.get('item_name', '')}' 已應用到頁面調教欄位。")
                            st.rerun()
            else:
                st.warning("AI 分析結果不是預期的列表格式。")
        else:
            st.info("點擊 '💡 讓 AI 分析本頁內容' 以獲取 AI 建議。")

        st.markdown("---")
        st.markdown("### 人工頁面調教與保存")
        
        # 從 session_state 獲取頁面級人工調教數據
        current_manual_page_data = st.session_state.manual_page_data.get(page_key, {})

        manual_page_item_name = st.text_input(
            "頁面核心項目名稱", 
            value=current_manual_page_data.get('item_name', ''),
            key=f"page_item_name_{current_df_actual_index}"
        )
        current_page_category_idx = CATEGORY_OPTIONS.index(current_manual_page_data.get('category', '未分類')) if current_manual_page_data.get('category', '未分類') in CATEGORY_OPTIONS else 0
        manual_page_category = st.selectbox(
            "頁面核心分類", 
            options=CATEGORY_OPTIONS, 
            index=current_page_category_idx,
            key=f"page_category_{current_df_actual_index}"
        )
        manual_page_description = st.text_area(
            "頁面總結/精煉描述", 
            value=current_manual_page_data.get('description', ''), 
            height=150, 
            key=f"page_description_{current_df_actual_index}"
        )
        manual_page_level = st.text_input(
            "頁面層級", 
            value=current_manual_page_data.get('level', ''), 
            key=f"page_level_{current_df_actual_index}"
        )
        manual_page_parent_name = st.text_input(
            "頁面父級名稱", 
            value=current_manual_page_data.get('parent_name', ''), 
            key=f"page_parent_name_{current_df_actual_index}"
        )

        current_page_status_idx = REVIEW_STATUS_OPTIONS.index(current_manual_page_data.get('review_status', '待審核')) if current_manual_page_data.get('review_status', '待審核') in REVIEW_STATUS_OPTIONS else 0
        manual_page_review_status = st.selectbox(
            "頁面審核狀態", 
            options=REVIEW_STATUS_OPTIONS, 
            index=current_page_status_idx,
            key=f"page_review_status_{current_df_actual_index}"
        )
        manual_page_correction_notes = st.text_area(
            "頁面修正建議/備註", 
            value=current_manual_page_data.get('notes', ''), 
            height=100, 
            key=f"page_notes_{current_df_actual_index}"
        )

        if st.button("保存此頁調教數據", use_container_width=True):
            # 保存頁面級調教數據到 session_state
            st.session_state.manual_page_data[page_key] = {
                'item_name': manual_page_item_name,
                'category': manual_page_category,
                'description': manual_page_description,
                'level': manual_page_level,
                'parent_name': manual_page_parent_name,
                'review_status': manual_page_review_status,
                'notes': manual_page_correction_notes,
                'LastProcessedDate': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            # 同步更新到 df_fragments 中的所有屬於此頁的記錄的相關欄位
            # 這一步是關鍵：將頁面級的人工調教結果「下沉」到所有屬於該頁的知識片段中
            for idx in current_page_fragments_df.index:
                st.session_state.df_fragments.loc[idx, 'PotentialItemName'] = manual_page_item_name
                st.session_state.df_fragments.loc[idx, 'PotentialCategory'] = manual_page_category
                st.session_state.df_fragments.loc[idx, 'ExtractedDescription'] = manual_page_description # 頁面級描述可以作為所有片段的描述
                st.session_state.df_fragments.loc[idx, 'SuggestedLevel'] = manual_page_level
                st.session_state.df_fragments.loc[idx, 'SuggestedParentName'] = manual_page_parent_name
                st.session_state.df_fragments.loc[idx, '人工審核狀態'] = manual_page_review_status
                st.session_state.df_fragments.loc[idx, 'ManualCorrectionNotes'] = manual_page_correction_notes
                st.session_state.df_fragments.loc[idx, 'LastProcessedDate'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')


            save_data(st.session_state.df_fragments, DATA_OUTPUT_PATH)
            st.success("頁面數據調教已保存！")
            st.rerun() 


if __name__ == "__main__":
    main()